#!/bin/bash

# Script to send multiple requests to the GoMAD /read endpoint
# Usage: ./scripts/read_test_messages.sh [base_url] [num_requests] [delay_seconds]

set -e

# Configuration
BASE_URL="${1:-http://localhost:8080}"
NUM_REQUESTS="${2:-100}"
DELAY_SECONDS="${3:-1}"
ENDPOINT="${BASE_URL}/read"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Counters
SUCCESS_COUNT=0
EMPTY_COUNT=0
ERROR_COUNT=0
MESSAGES_RETRIEVED=0
TOTAL_TIME=0

echo -e "${BLUE}Starting read test...${NC}"
echo -e "${BLUE}Target: ${ENDPOINT}${NC}"
echo -e "${BLUE}Number of requests: ${NUM_REQUESTS}${NC}"
echo -e "${BLUE}Delay between requests: ${DELAY_SECONDS}s${NC}"
echo -e "${BLUE}$(date)${NC}"
echo ""

# Function to send a single read request
send_read_request() {
    local request_id=$1
    local start_time=$(date +%s)
    
    local response=$(curl -s -w "\n%{http_code}\n%{time_total}" \
        -X GET "${ENDPOINT}")
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    # Parse response
    local body=$(echo "$response" | head -n -2)
    local http_code=$(echo "$response" | tail -n 2 | head -n 1)
    local curl_time=$(echo "$response" | tail -n 1)
    
    case "$http_code" in
        "200")
            SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
            MESSAGES_RETRIEVED=$((MESSAGES_RETRIEVED + 1))
            echo -e "${GREEN}✓${NC} Request $request_id: Message retrieved (${duration}s)"
            
            # Extract message details from response
            local message_id=$(echo "$body" | grep -o '"message_id":"[^"]*"' | cut -d'"' -f4)
            local order_id=$(echo "$body" | grep -o '"orderId":"[^"]*"' | cut -d'"' -f4)
            local customer_id=$(echo "$body" | grep -o '"customerId":"[^"]*"' | cut -d'"' -f4)
            
            if [[ -n "$message_id" ]]; then
                echo -e "  ${CYAN}Message ID:${NC} $message_id"
            fi
            if [[ -n "$order_id" ]]; then
                echo -e "  ${CYAN}Order ID:${NC} $order_id"
            fi
            if [[ -n "$customer_id" ]]; then
                echo -e "  ${CYAN}Customer ID:${NC} $customer_id"
            fi
            ;;
        "204")
            EMPTY_COUNT=$((EMPTY_COUNT + 1))
            echo -e "${YELLOW}○${NC} Request $request_id: Queue empty (${duration}s)"
            ;;
        *)
            ERROR_COUNT=$((ERROR_COUNT + 1))
            echo -e "${RED}✗${NC} Request $request_id: Error ${http_code} (${duration}s)"
            if [[ -n "$body" ]]; then
                echo "  Response: $body"
            fi
            ;;
    esac
    
    TOTAL_TIME=$((TOTAL_TIME + duration))
}

# Function to check queue status
check_queue_status() {
    echo -e "${YELLOW}Checking initial queue status...${NC}"
    local response=$(curl -s -w "%{http_code}" "${ENDPOINT}")
    local http_code=$(echo "$response" | tail -c 4)
    
    case "$http_code" in
        "200")
            echo -e "${GREEN}Queue has messages available${NC}"
            ;;
        "204")
            echo -e "${YELLOW}Queue is currently empty${NC}"
            ;;
        *)
            echo -e "${RED}Error checking queue status: $http_code${NC}"
            ;;
    esac
    echo ""
}

# Function to show real-time statistics
show_progress() {
    local current=$1
    local total=$2
    local messages=$3
    local empty=$4
    local errors=$5
    
    echo -e "${BLUE}Progress: $current/$total | Messages: ${GREEN}$messages${NC} | Empty: ${YELLOW}$empty${NC} | Errors: ${RED}$errors${NC}"
}

# Main execution
check_queue_status

echo -e "${YELLOW}Starting read requests...${NC}"
echo ""

start_overall=$(date +%s)

# Send read requests
for i in $(seq 1 $NUM_REQUESTS); do
    send_read_request $i
    
    # Progress indicator every 5 requests
    if (( i % 5 == 0 )); then
        show_progress $i $NUM_REQUESTS $MESSAGES_RETRIEVED $EMPTY_COUNT $ERROR_COUNT
        echo ""
    fi
    
    # Delay between requests (except for the last one)
    if [[ $i -lt $NUM_REQUESTS ]]; then
        sleep $DELAY_SECONDS
    fi
done

end_overall=$(date +%s)
overall_duration=$((end_overall - start_overall))

# Summary
echo ""
echo -e "${BLUE}=== Read Test Summary ===${NC}"
echo -e "${BLUE}$(date)${NC}"
echo ""
echo -e "Total requests: ${NUM_REQUESTS}"
echo -e "${GREEN}Messages retrieved: ${MESSAGES_RETRIEVED}${NC}"
echo -e "${YELLOW}Empty responses (204): ${EMPTY_COUNT}${NC}"
echo -e "${RED}Errors: ${ERROR_COUNT}${NC}"

if [[ $NUM_REQUESTS -gt 0 ]]; then
    success_rate=$(( ((SUCCESS_COUNT + EMPTY_COUNT) * 100) / NUM_REQUESTS ))
    message_rate=$(( (MESSAGES_RETRIEVED * 100) / NUM_REQUESTS ))
    echo -e "Success rate (200+204): ${success_rate}%"
    echo -e "Message retrieval rate: ${message_rate}%"
fi

echo ""
echo -e "Overall duration: ${overall_duration}s"
if [[ $NUM_REQUESTS -gt 0 ]]; then
    avg_time=$(( TOTAL_TIME / NUM_REQUESTS ))
    echo -e "Average request time: ${avg_time}s"
fi
if [[ $overall_duration -gt 0 ]]; then
    rps=$(( NUM_REQUESTS / overall_duration ))
    echo -e "Requests per second: ${rps}"
fi

# Queue status after test
echo ""
echo -e "${YELLOW}Final queue status check...${NC}"
final_response=$(curl -s "${BASE_URL}/health" || echo "Health check failed")
echo "Health check response: $final_response"

# Recommendations
echo ""
echo -e "${BLUE}=== Recommendations ===${NC}"
if [[ $MESSAGES_RETRIEVED -eq 0 ]]; then
    echo -e "${YELLOW}No messages were retrieved. Consider:${NC}"
    echo "  1. Running the send_test_messages.sh script first"
    echo "  2. Checking if the queue has messages"
    echo "  3. Verifying SQS configuration"
elif [[ $EMPTY_COUNT -gt $MESSAGES_RETRIEVED ]]; then
    echo -e "${YELLOW}More empty responses than messages. Consider:${NC}"
    echo "  1. Reducing the delay between requests"
    echo "  2. Sending more messages before reading"
    echo "  3. Checking message processing time"
else
    echo -e "${GREEN}Good message retrieval rate!${NC}"
fi

# Exit with error code if there were failures
if [[ $ERROR_COUNT -gt 0 ]]; then
    echo -e "${RED}Read test completed with errors${NC}"
    exit 1
else
    echo -e "${GREEN}Read test completed successfully${NC}"
    exit 0
fi
