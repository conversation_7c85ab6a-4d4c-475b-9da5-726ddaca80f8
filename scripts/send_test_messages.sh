#!/bin/bash

# Script to send 100 test messages to the GoMAD queue endpoint
# Usage: ./scripts/send_test_messages.sh [base_url] [num_requests]

set -e

# Configuration
BASE_URL="${1:-http://localhost:8080}"
NUM_REQUESTS="${2:-100}"
ENDPOINT="${BASE_URL}/queue"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
SUCCESS_COUNT=0
ERROR_COUNT=0
TOTAL_TIME=0

echo -e "${BLUE}Starting load test...${NC}"
echo -e "${BLUE}Target: ${ENDPOINT}${NC}"
echo -e "${BLUE}Number of requests: ${NUM_REQUESTS}${NC}"
echo -e "${BLUE}$(date)${NC}"
echo ""

# Function to generate random test data
generate_test_data() {
    local request_id=$1
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    local customer_id="customer-$(printf "%05d" $((RANDOM % 99999)))"
    local order_id="order-$(printf "%05d" $request_id)"
    local amount_cents=$((RANDOM % 50000))
    local amount=$(awk "BEGIN {printf \"%.2f\", $amount_cents/100}")
    local price_cents=$((RANDOM % 10000))
    local price=$(awk "BEGIN {printf \"%.2f\", $price_cents/100}")

    cat <<EOF
{
  "orderId": "${order_id}",
  "customerId": "${customer_id}",
  "amount": ${amount},
  "items": [
    {
      "productId": "prod-$(printf "%03d" $((RANDOM % 999)))",
      "quantity": $((RANDOM % 5 + 1)),
      "price": ${price}
    }
  ],
  "metadata": {
    "source": "load-test",
    "batch": "batch-$(date +%s)",
    "request_number": ${request_id},
    "timestamp": "${timestamp}"
  }
}
EOF
}

# Function to send a single request
send_request() {
    local request_id=$1
    local start_time=$(date +%s)

    local response=$(curl -s -w "\n%{http_code}\n%{time_total}" \
        -X POST "${ENDPOINT}" \
        -H "Content-Type: application/json" \
        -d "$(generate_test_data $request_id)")

    local end_time=$(date +%s)
    local duration=$((end_time - start_time))

    # Parse response
    local body=$(echo "$response" | head -n -2)
    local http_code=$(echo "$response" | tail -n 2 | head -n 1)
    local curl_time=$(echo "$response" | tail -n 1)

    if [[ "$http_code" == "200" ]]; then
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
        echo -e "${GREEN}✓${NC} Request $request_id: ${http_code} (${duration}s)"

        # Extract message_id from response if available
        local message_id=$(echo "$body" | grep -o '"message_id":"[^"]*"' | cut -d'"' -f4)
        if [[ -n "$message_id" ]]; then
            echo "  Message ID: $message_id"
        fi
    else
        ERROR_COUNT=$((ERROR_COUNT + 1))
        echo -e "${RED}✗${NC} Request $request_id: ${http_code} (${duration}s)"
        echo "  Response: $body"
    fi

    TOTAL_TIME=$((TOTAL_TIME + duration))
}

# Main execution
echo -e "${YELLOW}Sending requests...${NC}"
echo ""

start_overall=$(date +%s)

# Send requests sequentially
for i in $(seq 1 $NUM_REQUESTS); do
    send_request $i
    
    # Progress indicator every 10 requests
    if (( i % 10 == 0 )); then
        echo -e "${BLUE}Progress: $i/$NUM_REQUESTS requests completed${NC}"
        echo ""
    fi
    
    # Small delay to avoid overwhelming the server
    sleep 0.1
done

end_overall=$(date +%s)
overall_duration=$((end_overall - start_overall))

# Summary
echo ""
echo -e "${BLUE}=== Load Test Summary ===${NC}"
echo -e "${BLUE}$(date)${NC}"
echo ""
echo -e "Total requests: ${NUM_REQUESTS}"
echo -e "${GREEN}Successful: ${SUCCESS_COUNT}${NC}"
echo -e "${RED}Failed: ${ERROR_COUNT}${NC}"
if [[ $NUM_REQUESTS -gt 0 ]]; then
    local success_rate=$(( (SUCCESS_COUNT * 100) / NUM_REQUESTS ))
    echo -e "Success rate: ${success_rate}%"
fi
echo ""
echo -e "Overall duration: ${overall_duration}s"
if [[ $NUM_REQUESTS -gt 0 ]]; then
    local avg_time=$(( TOTAL_TIME / NUM_REQUESTS ))
    echo -e "Average request time: ${avg_time}s"
fi
if [[ $overall_duration -gt 0 ]]; then
    local rps=$(( NUM_REQUESTS / overall_duration ))
    echo -e "Requests per second: ${rps}"
fi
echo ""

# Health check after load test
echo -e "${YELLOW}Checking server health...${NC}"
health_response=$(curl -s "${BASE_URL}/health" || echo "Health check failed")
echo "Health check response: $health_response"

# Exit with error code if there were failures
if [[ $ERROR_COUNT -gt 0 ]]; then
    echo -e "${RED}Load test completed with errors${NC}"
    exit 1
else
    echo -e "${GREEN}Load test completed successfully${NC}"
    exit 0
fi
