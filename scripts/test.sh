#!/bin/bash

# Comprehensive test execution script for Go SQS API
# Usage: ./scripts/test.sh [test-type] [options]

set -e

# Default values
TEST_TYPE="all"
VERBOSE=false
COVERAGE=false
INTEGRATION=false
SECURITY=false
PERFORMANCE=false
TIMEOUT="30s"
LOCALSTACK=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Show usage information
show_usage() {
    cat << EOF
Go SQS API Test Runner

Usage: $0 [test-type] [options]

Test Types:
  unit          Run unit tests only
  integration   Run integration tests only
  security      Run security scans
  performance   Run performance/benchmark tests
  all           Run all tests (default)

Options:
  -v, --verbose     Enable verbose output
  -c, --coverage    Generate coverage report
  -t, --timeout     Set test timeout (default: 30s)
  -l, --localstack  Use LocalStack for integration tests
  -h, --help        Show this help message

Examples:
  $0 unit -v -c                    # Run unit tests with verbose output and coverage
  $0 integration --localstack      # Run integration tests with LocalStack
  $0 all -v -c -t 60s              # Run all tests with coverage and 60s timeout
  $0 security                      # Run security scans only

Environment Variables:
  USE_REAL_SQS=true               # Use real AWS SQS for integration tests
  AWS_REGION=us-east-1            # AWS region for tests
  SQS_QUEUE_URL=...               # SQS queue URL for integration tests
  GO_TEST_TIMEOUT=30s             # Override default test timeout

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            unit|integration|security|performance|all)
                TEST_TYPE="$1"
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -c|--coverage)
                COVERAGE=true
                shift
                ;;
            -t|--timeout)
                TIMEOUT="$2"
                shift 2
                ;;
            -l|--localstack)
                LOCALSTACK=true
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
}

# Validate environment
validate_environment() {
    log_info "Validating test environment..."
    
    # Check if Go is installed
    if ! command -v go &> /dev/null; then
        log_error "Go is not installed or not in PATH"
        exit 1
    fi
    
    # Check Go version
    GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
    log_info "Go version: $GO_VERSION"
    
    # Check if we're in the project root
    if [ ! -f "go.mod" ]; then
        log_error "Must be run from project root directory"
        exit 1
    fi
    
    log_success "Environment validation passed"
}

# Setup test environment
setup_test_environment() {
    log_info "Setting up test environment..."
    
    # Create test directories if they don't exist
    mkdir -p test/coverage test/reports
    
    # Set test environment variables
    export GO_ENV=test
    export LOG_LEVEL=debug
    
    # Override timeout if set via environment
    if [ -n "$GO_TEST_TIMEOUT" ]; then
        TIMEOUT="$GO_TEST_TIMEOUT"
    fi
    
    log_success "Test environment setup complete"
}

# Run unit tests
run_unit_tests() {
    log_info "Running unit tests..."
    
    local test_args="-timeout $TIMEOUT"
    
    if [ "$VERBOSE" = true ]; then
        test_args="$test_args -v"
    fi
    
    if [ "$COVERAGE" = true ]; then
        test_args="$test_args -coverprofile=test/coverage/unit.out -covermode=atomic"
    fi
    
    # Run unit tests (exclude integration tests)
    go test $test_args ./internal/... ./cmd/...
    
    if [ "$COVERAGE" = true ]; then
        # Generate coverage report
        go tool cover -html=test/coverage/unit.out -o test/coverage/unit.html
        go tool cover -func=test/coverage/unit.out | tail -1
        log_success "Unit test coverage report generated: test/coverage/unit.html"
    fi
    
    log_success "Unit tests completed"
}

# Run integration tests
run_integration_tests() {
    log_info "Running integration tests..."
    
    if [ "$LOCALSTACK" = true ]; then
        log_info "Starting LocalStack for integration tests..."
        run_integration_with_localstack
    else
        run_integration_direct
    fi
}

# Run integration tests with LocalStack
run_integration_with_localstack() {
    log_info "Using LocalStack for integration tests..."
    
    # Start LocalStack environment
    cd test/integration
    docker-compose -f docker-compose.test.yml up --build --abort-on-container-exit
    local exit_code=$?
    
    # Cleanup
    docker-compose -f docker-compose.test.yml down -v
    cd ../..
    
    if [ $exit_code -eq 0 ]; then
        log_success "Integration tests with LocalStack completed"
    else
        log_error "Integration tests with LocalStack failed"
        exit $exit_code
    fi
}

# Run integration tests directly
run_integration_direct() {
    local test_args="-timeout $TIMEOUT"
    
    if [ "$VERBOSE" = true ]; then
        test_args="$test_args -v"
    fi
    
    if [ "$COVERAGE" = true ]; then
        test_args="$test_args -coverprofile=test/coverage/integration.out -covermode=atomic"
    fi
    
    # Run integration tests
    go test $test_args ./test/integration/...
    
    if [ "$COVERAGE" = true ]; then
        # Generate coverage report
        go tool cover -html=test/coverage/integration.out -o test/coverage/integration.html
        log_success "Integration test coverage report generated: test/coverage/integration.html"
    fi
    
    log_success "Integration tests completed"
}

# Run security scans
run_security_tests() {
    log_info "Running security scans..."
    
    # Install gosec if not present
    if ! command -v gosec &> /dev/null; then
        log_info "Installing gosec security scanner..."
        go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest
    fi
    
    # Run gosec security scan
    gosec -fmt json -out test/reports/security.json ./...
    gosec -fmt text ./...
    
    log_success "Security scan completed - report saved to test/reports/security.json"
}

# Run performance tests
run_performance_tests() {
    log_info "Running performance tests..."
    
    local bench_args="-bench=. -benchmem -timeout $TIMEOUT"
    
    if [ "$VERBOSE" = true ]; then
        bench_args="$bench_args -v"
    fi
    
    # Run benchmark tests
    go test $bench_args ./internal/... ./test/... | tee test/reports/benchmark.txt
    
    log_success "Performance tests completed - results saved to test/reports/benchmark.txt"
}

# Generate combined coverage report
generate_combined_coverage() {
    if [ "$COVERAGE" = true ]; then
        log_info "Generating combined coverage report..."
        
        # Combine coverage files if both exist
        if [ -f "test/coverage/unit.out" ] && [ -f "test/coverage/integration.out" ]; then
            echo "mode: atomic" > test/coverage/combined.out
            tail -n +2 test/coverage/unit.out >> test/coverage/combined.out
            tail -n +2 test/coverage/integration.out >> test/coverage/combined.out
            
            go tool cover -html=test/coverage/combined.out -o test/coverage/combined.html
            go tool cover -func=test/coverage/combined.out | tail -1
            
            log_success "Combined coverage report generated: test/coverage/combined.html"
        fi
    fi
}

# Main execution function
main() {
    log_info "Starting Go SQS API test execution..."
    log_info "Test type: $TEST_TYPE"
    log_info "Timeout: $TIMEOUT"
    
    validate_environment
    setup_test_environment
    
    case $TEST_TYPE in
        unit)
            run_unit_tests
            ;;
        integration)
            run_integration_tests
            ;;
        security)
            run_security_tests
            ;;
        performance)
            run_performance_tests
            ;;
        all)
            run_unit_tests
            run_integration_tests
            run_security_tests
            run_performance_tests
            generate_combined_coverage
            ;;
        *)
            log_error "Unknown test type: $TEST_TYPE"
            show_usage
            exit 1
            ;;
    esac
    
    log_success "Test execution completed successfully!"
}

# Parse arguments and run
parse_args "$@"
main
