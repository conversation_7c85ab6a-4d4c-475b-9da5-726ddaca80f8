#!/bin/bash

# Docker build script for Go SQS API
# Usage: ./scripts/docker-build.sh [tag]

set -e

# Default values
DEFAULT_TAG="gomad:latest"
DOCKERFILE_PATH="deployments/Dockerfile"
CONTEXT_PATH="."

# Parse arguments
TAG=${1:-$DEFAULT_TAG}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Validate environment
validate_environment() {
    log_info "Validating build environment..."
    
    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running"
        exit 1
    fi
    
    # Check if Dockerfile exists
    if [ ! -f "$DOCKERFILE_PATH" ]; then
        log_error "Dockerfile not found at $DOCKERFILE_PATH"
        exit 1
    fi
    
    log_success "Environment validation passed"
}

# Build Docker image
build_image() {
    log_info "Building Docker image with tag: $TAG"
    log_info "Using Dockerfile: $DOCKERFILE_PATH"
    log_info "Build context: $CONTEXT_PATH"
    
    # Build the image
    docker build \
        -f "$DOCKERFILE_PATH" \
        -t "$TAG" \
        "$CONTEXT_PATH"
    
    if [ $? -eq 0 ]; then
        log_success "Docker image built successfully: $TAG"
    else
        log_error "Docker build failed"
        exit 1
    fi
}

# Display image information
show_image_info() {
    log_info "Image information:"
    docker images "$TAG" --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.CreatedAt}}\t{{.Size}}"
}

# Test the built image
test_image() {
    log_info "Testing the built image..."
    
    # Run a quick test to ensure the image starts
    CONTAINER_ID=$(docker run -d -p 8081:8080 "$TAG")
    
    if [ $? -eq 0 ]; then
        log_info "Container started with ID: $CONTAINER_ID"
        
        # Wait a moment for startup
        sleep 5
        
        # Test health endpoint
        if curl -f http://localhost:8081/health &> /dev/null; then
            log_success "Health check passed"
        else
            log_warning "Health check failed - this may be expected if AWS credentials are not configured"
        fi
        
        # Clean up test container
        docker stop "$CONTAINER_ID" &> /dev/null
        docker rm "$CONTAINER_ID" &> /dev/null
        log_info "Test container cleaned up"
    else
        log_error "Failed to start test container"
        exit 1
    fi
}

# Main execution
main() {
    log_info "Starting Docker build process..."
    
    validate_environment
    build_image
    show_image_info
    test_image
    
    log_success "Docker build completed successfully!"
    log_info "You can now run the container with:"
    log_info "  docker run -p 8080:8080 $TAG"
    log_info "Or use docker-compose:"
    log_info "  cd deployments && docker-compose up"
}

# Run main function
main "$@"
