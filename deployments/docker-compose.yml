version: '3.8'

services:
  # Go SQS API Service
  gomad-api:
    build:
      context: ..
      dockerfile: deployments/Dockerfile
    ports:
      - "8080:8080"
    environment:
      - PORT=8080
      - LOG_LEVEL=debug
      - AWS_REGION=us-east-1
      - SQS_QUEUE_URL=http://localstack:4566/000000000000/gomad-test-queue.fifo
      - MESSAGE_GROUP_ID=gomad-dev-group
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
      - AWS_ENDPOINT_URL=http://localstack:4566
    depends_on:
      - localstack
    networks:
      - gomad-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # LocalStack for AWS SQS emulation
  localstack:
    image: localstack/localstack:3.0
    ports:
      - "4566:4566"
    environment:
      - SERVICES=sqs
      - DEBUG=1
      - DATA_DIR=/tmp/localstack/data
      - DOCKER_HOST=unix:///var/run/docker.sock
    volumes:
      - localstack-data:/tmp/localstack
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - gomad-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4566/_localstack/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # SQS Queue Setup (runs once to create the FIFO queue)
  sqs-setup:
    image: amazon/aws-cli:2.15.17
    environment:
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
      - AWS_DEFAULT_REGION=us-east-1
    command: >
      sh -c "
        echo 'Waiting for LocalStack to be ready...' &&
        sleep 10 &&
        aws --endpoint-url=http://localstack:4566 sqs create-queue 
          --queue-name gomad-test-queue.fifo 
          --attributes FifoQueue=true,ContentBasedDeduplication=false &&
        echo 'FIFO queue created successfully'
      "
    depends_on:
      - localstack
    networks:
      - gomad-network
    restart: "no"

networks:
  gomad-network:
    driver: bridge

volumes:
  localstack-data:
