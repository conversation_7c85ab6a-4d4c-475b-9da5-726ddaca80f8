# Git
.git
.gitignore

# Documentation
README.md
docs/
*.md

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Build artifacts
bin/
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test files
*_test.go
test/
coverage.out
*.test

# Temporary files
*.tmp
*.temp
.tmp/

# Environment files
.env
.env.local
.env.*.local

# Docker files (avoid recursive copying)
Dockerfile*
docker-compose*.yml
.dockerignore

# CI/CD
.github/
.circleci/

# Scripts (not needed in container)
scripts/

# Development tools
Makefile
