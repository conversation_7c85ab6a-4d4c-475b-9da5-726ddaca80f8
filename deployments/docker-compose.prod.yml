version: '3.8'

services:
  # Go SQS API Service - Production Configuration
  gomad-api:
    build:
      context: ..
      dockerfile: deployments/Dockerfile
    ports:
      - "8080:8080"
    environment:
      - PORT=8080
      - LOG_LEVEL=info
      - AWS_REGION=${AWS_REGION:-us-east-1}
      - SQS_QUEUE_URL=${SQS_QUEUE_URL}
      - MESSAGE_GROUP_ID=${MESSAGE_GROUP_ID:-gomad-prod-group}
    networks:
      - gomad-network
    restart: unless-stopped
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 128M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
        monitor: 60s
        max_failure_ratio: 0.3
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Load Balancer (optional - for multi-replica deployment)
  nginx:
    image: nginx:1.25-alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - gomad-api
    networks:
      - gomad-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.2'
          memory: 64M
        reservations:
          cpus: '0.1'
          memory: 32M
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 5s
      retries: 3

networks:
  gomad-network:
    driver: bridge
