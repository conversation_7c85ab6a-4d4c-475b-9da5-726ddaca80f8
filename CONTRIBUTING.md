# Contributing to GoMAD

We welcome contributions to GoMAD! This document provides guidelines for contributing to the project.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Setup](#development-setup)
- [Making Changes](#making-changes)
- [Testing](#testing)
- [Submitting Changes](#submitting-changes)
- [Code Review Process](#code-review-process)
- [Style Guidelines](#style-guidelines)
- [Documentation](#documentation)

## Code of Conduct

This project adheres to a code of conduct. By participating, you are expected to uphold this code. Please report unacceptable behavior to the project maintainers.

### Our Standards

- Use welcoming and inclusive language
- Be respectful of differing viewpoints and experiences
- Gracefully accept constructive criticism
- Focus on what is best for the community
- Show empathy towards other community members

## Getting Started

### Prerequisites

- Go 1.21 or higher
- Git
- AWS CLI (for testing with real SQS)
- Docker (optional, for containerized development)

### Fork and Clone

1. Fork the repository on GitHub
2. Clone your fork locally:
   ```bash
   git clone https://github.com/your-username/gomad.git
   cd gomad
   ```
3. Add the upstream repository:
   ```bash
   git remote add upstream https://github.com/original-owner/gomad.git
   ```

## Development Setup

### Install Dependencies

```bash
# Download Go modules
go mod download

# Install development tools
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
go install github.com/stretchr/testify/...@latest
```

### Environment Configuration

Create a `.env` file for local development:

```bash
# Copy example environment file
cp .env.example .env

# Edit with your configuration
export PORT=8080
export LOG_LEVEL=debug
export AWS_REGION=us-east-1
export SQS_QUEUE_URL=https://sqs.us-east-1.amazonaws.com/123456789012/test-queue.fifo
export MESSAGE_GROUP_ID=development
```

### Run Locally

```bash
# Load environment variables
source .env

# Run the application
go run cmd/server/main.go

# Or build and run
go build -o bin/gomad cmd/server/main.go
./bin/gomad
```

## Making Changes

### Branch Naming

Use descriptive branch names:

- `feature/add-batch-processing`
- `bugfix/fix-memory-leak`
- `docs/update-api-documentation`
- `refactor/improve-error-handling`

### Commit Messages

Follow conventional commit format:

```
type(scope): description

[optional body]

[optional footer]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

**Examples:**
```
feat(api): add batch message processing endpoint

Add new /batch endpoint that accepts multiple messages
and processes them in a single SQS batch operation.

Closes #123
```

```
fix(handlers): resolve memory leak in message processing

The message handler was not properly releasing memory
after processing large JSON payloads.

Fixes #456
```

### Making Changes

1. Create a new branch:
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. Make your changes following the style guidelines

3. Add or update tests for your changes

4. Ensure all tests pass:
   ```bash
   go test ./...
   ```

5. Run linting:
   ```bash
   golangci-lint run
   ```

6. Commit your changes:
   ```bash
   git add .
   git commit -m "feat(scope): your descriptive message"
   ```

## Testing

### Test Categories

1. **Unit Tests**: Test individual components in isolation
2. **Integration Tests**: Test component interactions
3. **Performance Tests**: Validate performance requirements
4. **FIFO Tests**: Validate message ordering

### Running Tests

```bash
# Run all tests
go test ./...

# Run tests with coverage
go test ./... -cover

# Run specific test suite
go test ./test/integration/ -v

# Run tests with race detection
go test ./... -race

# Generate coverage report
go test ./... -coverprofile=coverage.out
go tool cover -html=coverage.out
```

### Writing Tests

#### Unit Test Example

```go
func TestQueueHandler_HandlePostQueue(t *testing.T) {
    // Arrange
    mockSQS := &mocks.MockSQSService{}
    handler := handlers.NewQueueHandler(mockSQS)
    
    mockSQS.SetSendMessageResponse("test-message-id", nil)
    
    payload := `{"orderId": "test-123", "amount": 99.99}`
    req := httptest.NewRequest("POST", "/queue", strings.NewReader(payload))
    req.Header.Set("Content-Type", "application/json")
    
    recorder := httptest.NewRecorder()
    
    // Act
    handler.HandlePostQueue(recorder, req)
    
    // Assert
    assert.Equal(t, http.StatusOK, recorder.Code)
    
    var response models.APIResponse
    err := json.Unmarshal(recorder.Body.Bytes(), &response)
    assert.NoError(t, err)
    assert.True(t, response.Success)
}
```

#### Integration Test Example

```go
func TestAPIIntegration_EndToEnd(t *testing.T) {
    // Setup test server
    suite := &APITestSuite{}
    suite.SetupSuite()
    defer suite.TearDownSuite()
    
    // Submit message
    payload := map[string]interface{}{
        "orderId": "integration-test-123",
        "amount":  99.99,
    }
    
    submitResp := suite.submitMessage(payload)
    assert.Equal(t, http.StatusOK, submitResp.StatusCode)
    
    // Retrieve message
    readResp := suite.readMessage()
    assert.Equal(t, http.StatusOK, readResp.StatusCode)
    
    // Validate message content
    var response models.APIResponse
    json.NewDecoder(readResp.Body).Decode(&response)
    
    messageData := response.Data.(map[string]interface{})
    body := messageData["body"].(map[string]interface{})
    
    assert.Equal(t, "integration-test-123", body["orderId"])
    assert.Equal(t, 99.99, body["amount"])
}
```

### Test Coverage Requirements

- Minimum 80% overall coverage
- 90% coverage for critical paths (handlers, services)
- 100% coverage for new features
- All public functions must have tests

## Submitting Changes

### Pull Request Process

1. Update your branch with the latest upstream changes:
   ```bash
   git fetch upstream
   git rebase upstream/main
   ```

2. Push your branch to your fork:
   ```bash
   git push origin feature/your-feature-name
   ```

3. Create a pull request on GitHub with:
   - Clear title and description
   - Reference to related issues
   - Screenshots for UI changes
   - Performance impact notes

### Pull Request Template

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Performance tests pass
- [ ] Manual testing completed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] Tests added/updated
- [ ] No breaking changes (or documented)

## Related Issues
Closes #123
```

## Code Review Process

### Review Criteria

1. **Functionality**: Does the code work as intended?
2. **Performance**: Does it meet performance requirements?
3. **Security**: Are there any security concerns?
4. **Maintainability**: Is the code readable and maintainable?
5. **Testing**: Are there adequate tests?
6. **Documentation**: Is documentation updated?

### Review Timeline

- Initial review within 2 business days
- Follow-up reviews within 1 business day
- Approval required from at least one maintainer
- All CI checks must pass

### Addressing Feedback

1. Make requested changes
2. Respond to comments
3. Request re-review when ready
4. Be open to suggestions and discussions

## Style Guidelines

### Go Code Style

Follow standard Go conventions:

1. **Formatting**: Use `gofmt` and `goimports`
2. **Naming**: Use camelCase for variables, PascalCase for exported functions
3. **Comments**: Document all exported functions and types
4. **Error Handling**: Always handle errors explicitly
5. **Package Organization**: Keep packages focused and cohesive

### Code Examples

#### Good Example

```go
// QueueHandler handles HTTP requests for message queuing operations.
type QueueHandler struct {
    sqsService services.SQSServicer
    logger     *zap.Logger
}

// NewQueueHandler creates a new QueueHandler with the provided dependencies.
func NewQueueHandler(sqsService services.SQSServicer) *QueueHandler {
    return &QueueHandler{
        sqsService: sqsService,
        logger:     logger.GetLogger(),
    }
}

// HandlePostQueue processes POST requests to submit messages to the queue.
func (h *QueueHandler) HandlePostQueue(w http.ResponseWriter, r *http.Request) {
    requestID := middleware.GetRequestID(r.Context())
    
    if r.Method != http.MethodPost {
        h.writeErrorResponse(w, r, http.StatusMethodNotAllowed, "Only POST method is supported")
        return
    }
    
    // Process request...
}
```

#### Avoid

```go
// Bad: No comments, poor naming, no error handling
func (h *qh) handle(w http.ResponseWriter, r *http.Request) {
    body, _ := io.ReadAll(r.Body) // Ignoring error
    msg := string(body)
    h.sqs.Send(msg) // No error handling
    w.Write([]byte("ok"))
}
```

### Linting Rules

Use `golangci-lint` with the project configuration:

```bash
# Run linter
golangci-lint run

# Fix auto-fixable issues
golangci-lint run --fix
```

## Documentation

### Code Documentation

1. **Package Comments**: Every package should have a package comment
2. **Function Comments**: All exported functions need comments
3. **Type Comments**: All exported types need comments
4. **Example Code**: Include examples for complex functions

### API Documentation

Update API documentation when:
- Adding new endpoints
- Changing request/response formats
- Modifying error codes
- Adding new features

### README Updates

Update README.md for:
- New features
- Configuration changes
- Installation steps
- Usage examples

## Release Process

### Version Numbering

Follow semantic versioning (SemVer):
- `MAJOR.MINOR.PATCH`
- Major: Breaking changes
- Minor: New features (backward compatible)
- Patch: Bug fixes (backward compatible)

### Release Checklist

1. Update version in code
2. Update CHANGELOG.md
3. Create release notes
4. Tag the release
5. Build and publish artifacts
6. Update documentation

## Getting Help

### Communication Channels

- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: General questions and discussions
- **Pull Request Comments**: Code-specific discussions

### Maintainer Contact

For urgent issues or security concerns, contact the maintainers directly.

## Recognition

Contributors will be recognized in:
- CONTRIBUTORS.md file
- Release notes
- Project documentation

Thank you for contributing to GoMAD! 🚀
