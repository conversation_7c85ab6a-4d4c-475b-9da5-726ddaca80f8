# Security Policy

## Supported Versions

We actively support the following versions of GoMAD with security updates:

| Version | Supported          |
| ------- | ------------------ |
| 1.x.x   | :white_check_mark: |
| < 1.0   | :x:                |

## Reporting a Vulnerability

We take security vulnerabilities seriously. If you discover a security vulnerability in GoMAD, please report it responsibly.

### How to Report

**DO NOT** create a public GitHub issue for security vulnerabilities.

Instead, please:

1. **Email**: Send <NAME_EMAIL>
2. **Subject**: Include "SECURITY" in the subject line
3. **Details**: Provide as much information as possible:
   - Description of the vulnerability
   - Steps to reproduce
   - Potential impact
   - Suggested fix (if any)

### What to Expect

- **Acknowledgment**: We'll acknowledge receipt within 24 hours
- **Initial Assessment**: We'll provide an initial assessment within 72 hours
- **Updates**: We'll keep you informed of our progress
- **Resolution**: We aim to resolve critical issues within 7 days
- **Disclosure**: We'll coordinate public disclosure after the fix is available

### Security Measures

GoMAD implements several security measures:

#### Application Security

1. **Input Validation**
   - JSON schema validation
   - Content-Type enforcement
   - Request size limits (256KB max)
   - HTTP method validation

2. **Security Headers**
   ```
   X-Frame-Options: DENY
   X-Content-Type-Options: nosniff
   X-XSS-Protection: 1; mode=block
   Referrer-Policy: strict-origin-when-cross-origin
   ```

3. **CORS Configuration**
   - Configurable allowed origins
   - Method and header restrictions
   - Preflight request handling

4. **Error Handling**
   - No sensitive information in error responses
   - Structured error logging
   - Request correlation for debugging

#### Infrastructure Security

1. **Network Security**
   - TLS/SSL encryption in transit
   - Private subnet deployment
   - Security group restrictions
   - VPC endpoints for AWS services

2. **Container Security**
   - Minimal base images (Alpine Linux)
   - Non-root user execution
   - Read-only root filesystem
   - Capability dropping

3. **AWS Security**
   - IAM roles with least privilege
   - No embedded credentials
   - SQS queue access policies
   - Encryption at rest and in transit

#### Secrets Management

1. **Environment Variables**
   - No secrets in code or images
   - Runtime secret injection
   - Kubernetes secrets integration

2. **AWS Integration**
   - IAM roles for service accounts
   - Systems Manager Parameter Store
   - Secrets Manager integration

### Security Best Practices

#### For Deployment

1. **Network Configuration**
   ```yaml
   # Example security group rules
   Inbound:
     - Port 8080 from Load Balancer only
     - No direct internet access
   
   Outbound:
     - Port 443 to AWS services
     - Port 53 for DNS resolution
   ```

2. **Container Security**
   ```dockerfile
   # Use specific versions, not 'latest'
   FROM alpine:3.18
   
   # Create non-root user
   RUN adduser -D -s /bin/sh gomad
   USER gomad
   
   # Read-only root filesystem
   VOLUME ["/tmp"]
   ```

3. **Kubernetes Security**
   ```yaml
   securityContext:
     allowPrivilegeEscalation: false
     runAsNonRoot: true
     runAsUser: 1000
     capabilities:
       drop:
       - ALL
   ```

#### For Development

1. **Dependency Management**
   - Regular dependency updates
   - Vulnerability scanning
   - License compliance

2. **Code Security**
   - Static analysis tools
   - Secret scanning
   - Code review requirements

3. **Testing**
   - Security test cases
   - Penetration testing
   - Load testing for DoS resistance

### Vulnerability Disclosure Timeline

1. **Day 0**: Vulnerability reported
2. **Day 1**: Acknowledgment sent
3. **Day 3**: Initial assessment completed
4. **Day 7**: Fix developed and tested
5. **Day 14**: Security update released
6. **Day 21**: Public disclosure (if appropriate)

### Security Updates

Security updates are released as:

1. **Patch Releases**: For non-breaking security fixes
2. **Minor Releases**: For security improvements with new features
3. **Major Releases**: For security fixes requiring breaking changes

### Security Checklist for Contributors

Before submitting code:

- [ ] No hardcoded secrets or credentials
- [ ] Input validation for all user inputs
- [ ] Proper error handling without information leakage
- [ ] Security headers implemented
- [ ] Dependencies are up to date
- [ ] Static analysis tools pass
- [ ] Security tests included

### Common Security Considerations

#### Input Validation

```go
// Good: Validate input size and format
func validateMessage(body []byte) error {
    if len(body) == 0 {
        return errors.New("empty message body")
    }
    if len(body) > maxMessageSize {
        return errors.New("message too large")
    }
    
    var msg map[string]interface{}
    if err := json.Unmarshal(body, &msg); err != nil {
        return fmt.Errorf("invalid JSON: %w", err)
    }
    
    return nil
}
```

#### Error Handling

```go
// Good: Don't expose internal details
func handleError(w http.ResponseWriter, err error) {
    logger.Error("Internal error", zap.Error(err))
    
    response := APIResponse{
        Success: false,
        Message: "Internal server error",
    }
    
    w.WriteHeader(http.StatusInternalServerError)
    json.NewEncoder(w).Encode(response)
}
```

#### Logging Security

```go
// Good: Sanitize sensitive data
func logRequest(r *http.Request) {
    logger.Info("Request received",
        zap.String("method", r.Method),
        zap.String("path", r.URL.Path),
        zap.String("user_agent", r.UserAgent()),
        // Don't log: Authorization headers, request body, etc.
    )
}
```

### Security Resources

- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [Go Security Checklist](https://github.com/securego/gosec)
- [AWS Security Best Practices](https://aws.amazon.com/security/security-resources/)
- [Container Security Best Practices](https://kubernetes.io/docs/concepts/security/)

### Contact Information

- **Security Team**: <EMAIL>
- **General Contact**: <EMAIL>
- **GitHub**: Create a private security advisory

### Acknowledgments

We appreciate security researchers who responsibly disclose vulnerabilities. Contributors will be acknowledged in our security advisories (with permission).

---

**Note**: This security policy is subject to change. Please check back regularly for updates.
