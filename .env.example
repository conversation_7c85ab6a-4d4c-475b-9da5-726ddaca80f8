# GoMAD Configuration Example
# Copy this file to .env and update with your values

# Server Configuration
PORT=8080
LOG_LEVEL=info

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key-id
AWS_SECRET_ACCESS_KEY=your-secret-access-key

# SQS Configuration
SQS_QUEUE_URL=https://sqs.us-east-1.amazonaws.com/123456789012/your-queue.fifo
MESSAGE_GROUP_ID=default-group

# Optional: AWS Profile (alternative to access keys)
# AWS_PROFILE=default

# Optional: Additional AWS Configuration
# AWS_SESSION_TOKEN=your-session-token
# AWS_ENDPOINT_URL=http://localhost:4566  # For LocalStack testing
