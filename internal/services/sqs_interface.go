package services

import (
	"context"
	"encoding/json"

	"gomad/internal/models"
)

// SQSServicer defines the interface for SQS FIFO queue operations
type SQSServicer interface {
	// SendMessage sends a JSON message to the SQS FIFO queue
	SendMessage(ctx context.Context, body json.RawMessage) (*models.SQSMessage, error)
	
	// ReceiveMessage receives a message from the SQS FIFO queue and automatically deletes it
	ReceiveMessage(ctx context.Context) (*models.SQSMessage, error)
	
	// GenerateMessageIDs generates MessageGroupId and MessageDeduplicationId for FIFO queue operations
	GenerateMessageIDs() (groupId, deduplicationId string)
	
	// ValidateConnection validates the connection to the SQS queue
	ValidateConnection(ctx context.Context) error
}
