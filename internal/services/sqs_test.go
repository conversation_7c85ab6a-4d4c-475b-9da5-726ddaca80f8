package services

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/aws/aws-sdk-go-v2/service/sqs/types"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gomad/internal/config"
	"gomad/internal/logger"
)

// MockSQSClient is a mock implementation of the SQS client for testing
type MockSQSClient struct {
	sendMessageFunc        func(ctx context.Context, params *sqs.SendMessageInput, optFns ...func(*sqs.Options)) (*sqs.SendMessageOutput, error)
	receiveMessageFunc     func(ctx context.Context, params *sqs.ReceiveMessageInput, optFns ...func(*sqs.Options)) (*sqs.ReceiveMessageOutput, error)
	deleteMessageFunc      func(ctx context.Context, params *sqs.DeleteMessageInput, optFns ...func(*sqs.Options)) (*sqs.DeleteMessageOutput, error)
	getQueueAttributesFunc func(ctx context.Context, params *sqs.GetQueueAttributesInput, optFns ...func(*sqs.Options)) (*sqs.GetQueueAttributesOutput, error)
}

func (m *MockSQSClient) SendMessage(ctx context.Context, params *sqs.SendMessageInput, optFns ...func(*sqs.Options)) (*sqs.SendMessageOutput, error) {
	if m.sendMessageFunc != nil {
		return m.sendMessageFunc(ctx, params, optFns...)
	}
	return &sqs.SendMessageOutput{
		MessageId: aws.String("test-message-id"),
	}, nil
}

func (m *MockSQSClient) ReceiveMessage(ctx context.Context, params *sqs.ReceiveMessageInput, optFns ...func(*sqs.Options)) (*sqs.ReceiveMessageOutput, error) {
	if m.receiveMessageFunc != nil {
		return m.receiveMessageFunc(ctx, params, optFns...)
	}
	return &sqs.ReceiveMessageOutput{
		Messages: []types.Message{
			{
				MessageId:     aws.String("test-message-id"),
				Body:          aws.String(`{"test": "data"}`),
				ReceiptHandle: aws.String("test-receipt-handle"),
			},
		},
	}, nil
}

func (m *MockSQSClient) DeleteMessage(ctx context.Context, params *sqs.DeleteMessageInput, optFns ...func(*sqs.Options)) (*sqs.DeleteMessageOutput, error) {
	if m.deleteMessageFunc != nil {
		return m.deleteMessageFunc(ctx, params, optFns...)
	}
	return &sqs.DeleteMessageOutput{}, nil
}

func (m *MockSQSClient) GetQueueAttributes(ctx context.Context, params *sqs.GetQueueAttributesInput, optFns ...func(*sqs.Options)) (*sqs.GetQueueAttributesOutput, error) {
	if m.getQueueAttributesFunc != nil {
		return m.getQueueAttributesFunc(ctx, params, optFns...)
	}
	return &sqs.GetQueueAttributesOutput{
		Attributes: map[string]string{
			string(types.QueueAttributeNameQueueArn):  "arn:aws:sqs:us-east-1:123456789012:test-queue.fifo",
			string(types.QueueAttributeNameFifoQueue): "true",
		},
	}, nil
}

func createTestSQSService(mockClient *MockSQSClient) *SQSService {
	cfg := &config.Configuration{
		AWSRegion:      "us-east-1",
		SQSQueueURL:    "https://sqs.us-east-1.amazonaws.com/123456789012/test-queue.fifo",
		MessageGroupID: "test-group",
	}

	log, _ := logger.NewLogger("info")

	// Create service with mock client
	service := &SQSService{
		client:         mockClient,
		config:         cfg,
		logger:         log,
		queueURL:       cfg.SQSQueueURL,
		messageGroupID: cfg.MessageGroupID,
	}

	return service
}

func TestNewSQSService(t *testing.T) {
	mockClient := &MockSQSClient{}
	cfg := &config.Configuration{
		SQSQueueURL:    "https://sqs.us-east-1.amazonaws.com/123456789012/test-queue.fifo",
		MessageGroupID: "test-group",
	}
	log, err := logger.NewLogger("info")
	require.NoError(t, err)

	service := NewSQSService(mockClient, cfg, log)

	assert.NotNil(t, service)
	assert.Equal(t, cfg.SQSQueueURL, service.queueURL)
	assert.Equal(t, cfg.MessageGroupID, service.messageGroupID)
}

func TestSQSService_SendMessage(t *testing.T) {
	mockClient := &MockSQSClient{}
	service := createTestSQSService(mockClient)

	testData := map[string]any{
		"orderId":    "12345",
		"customerId": "user-abc",
	}
	body, err := json.Marshal(testData)
	require.NoError(t, err)

	ctx := context.Background()
	result, err := service.SendMessage(ctx, json.RawMessage(body))

	require.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "test-message-id", result.ID)
	assert.Equal(t, json.RawMessage(body), result.Body)
	assert.Equal(t, "test-group", result.MessageGroupId)
	assert.NotEmpty(t, result.MessageDeduplicationId)
	assert.True(t, time.Since(result.Timestamp) < time.Second)
}

func TestSQSService_ReceiveMessage(t *testing.T) {
	mockClient := &MockSQSClient{}
	service := createTestSQSService(mockClient)

	ctx := context.Background()
	result, err := service.ReceiveMessage(ctx)

	require.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "test-message-id", result.ID)
	assert.Equal(t, json.RawMessage(`{"test": "data"}`), result.Body)
	assert.Equal(t, "test-receipt-handle", result.ReceiptHandle)
}

func TestSQSService_ReceiveMessage_NoMessages(t *testing.T) {
	mockClient := &MockSQSClient{
		receiveMessageFunc: func(ctx context.Context, params *sqs.ReceiveMessageInput, optFns ...func(*sqs.Options)) (*sqs.ReceiveMessageOutput, error) {
			return &sqs.ReceiveMessageOutput{
				Messages: []types.Message{}, // No messages
			}, nil
		},
	}
	service := createTestSQSService(mockClient)

	ctx := context.Background()
	result, err := service.ReceiveMessage(ctx)

	require.NoError(t, err)
	assert.Nil(t, result) // Should return nil when no messages
}

func TestSQSService_GenerateMessageIDs(t *testing.T) {
	mockClient := &MockSQSClient{}
	service := createTestSQSService(mockClient)

	groupId1, deduplicationId1 := service.GenerateMessageIDs()
	groupId2, deduplicationId2 := service.GenerateMessageIDs()

	// Group ID should be consistent
	assert.Equal(t, "test-group", groupId1)
	assert.Equal(t, "test-group", groupId2)
	assert.Equal(t, groupId1, groupId2)

	// Deduplication IDs should be unique
	assert.NotEqual(t, deduplicationId1, deduplicationId2)
	assert.NotEmpty(t, deduplicationId1)
	assert.NotEmpty(t, deduplicationId2)

	// Deduplication IDs should be valid length (32 chars from hash)
	assert.Len(t, deduplicationId1, 32)
	assert.Len(t, deduplicationId2, 32)
}

func TestSQSService_ValidateConnection(t *testing.T) {
	mockClient := &MockSQSClient{}
	service := createTestSQSService(mockClient)

	ctx := context.Background()
	err := service.ValidateConnection(ctx)

	assert.NoError(t, err)
}

func TestSQSService_ValidateConnection_NotFIFO(t *testing.T) {
	mockClient := &MockSQSClient{
		getQueueAttributesFunc: func(ctx context.Context, params *sqs.GetQueueAttributesInput, optFns ...func(*sqs.Options)) (*sqs.GetQueueAttributesOutput, error) {
			return &sqs.GetQueueAttributesOutput{
				Attributes: map[string]string{
					string(types.QueueAttributeNameQueueArn):  "arn:aws:sqs:us-east-1:123456789012:test-queue",
					string(types.QueueAttributeNameFifoQueue): "false", // Not a FIFO queue
				},
			}, nil
		},
	}
	service := createTestSQSService(mockClient)

	ctx := context.Background()
	err := service.ValidateConnection(ctx)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not a FIFO queue")
}

func TestSQSService_ValidateConnection_NoQueueURL(t *testing.T) {
	mockClient := &MockSQSClient{}
	service := createTestSQSService(mockClient)
	service.queueURL = "" // Clear queue URL

	ctx := context.Background()
	err := service.ValidateConnection(ctx)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "SQS queue URL is not configured")
}
