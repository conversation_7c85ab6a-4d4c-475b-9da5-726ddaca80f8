package services

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/aws/aws-sdk-go-v2/service/sqs/types"
	"github.com/google/uuid"
	"go.uber.org/zap"

	"gomad/internal/config"
	"gomad/internal/logger"
	"gomad/internal/models"
)

// SQSClient defines the interface for SQS client operations
type SQSClient interface {
	SendMessage(ctx context.Context, params *sqs.SendMessageInput, optFns ...func(*sqs.Options)) (*sqs.SendMessageOutput, error)
	ReceiveMessage(ctx context.Context, params *sqs.ReceiveMessageInput, optFns ...func(*sqs.Options)) (*sqs.ReceiveMessageOutput, error)
	DeleteMessage(ctx context.Context, params *sqs.DeleteMessageInput, optFns ...func(*sqs.Options)) (*sqs.DeleteMessageOutput, error)
	GetQueueAttributes(ctx context.Context, params *sqs.GetQueueAttributesInput, optFns ...func(*sqs.Options)) (*sqs.GetQueueAttributesOutput, error)
}

// SQSService implements the SQSServicer interface for AWS SQS FIFO queue operations
type SQSService struct {
	client         SQSClient
	config         *config.Configuration
	logger         *logger.Logger
	queueURL       string
	messageGroupID string
}

// NewSQSService creates a new SQS service instance
func NewSQSService(client SQSClient, cfg *config.Configuration, logger *logger.Logger) *SQSService {
	return &SQSService{
		client:         client,
		config:         cfg,
		logger:         logger,
		queueURL:       cfg.SQSQueueURL,
		messageGroupID: cfg.MessageGroupID,
	}
}

// SendMessage sends a JSON message to the SQS FIFO queue
func (s *SQSService) SendMessage(ctx context.Context, body json.RawMessage) (*models.SQSMessage, error) {
	// Create context with timeout
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	// Generate FIFO message IDs
	groupId, deduplicationId := s.GenerateMessageIDs()

	// Prepare SQS send message input
	input := &sqs.SendMessageInput{
		QueueUrl:               aws.String(s.queueURL),
		MessageBody:            aws.String(string(body)),
		MessageGroupId:         aws.String(groupId),
		MessageDeduplicationId: aws.String(deduplicationId),
	}

	// Send message to SQS
	result, err := s.client.SendMessage(ctx, input)
	if err != nil {
		s.logger.Error("Failed to send message to SQS",
			zap.Error(err),
			zap.String("queue_url", s.queueURL),
			zap.String("message_group_id", groupId),
		)
		return nil, fmt.Errorf("failed to send message to SQS: %w", err)
	}

	// Create SQS message response
	sqsMessage := &models.SQSMessage{
		ID:                     aws.ToString(result.MessageId),
		Body:                   body,
		MessageGroupId:         groupId,
		MessageDeduplicationId: deduplicationId,
		Timestamp:              time.Now(),
	}

	s.logger.Info("Message sent to SQS successfully",
		zap.String("message_id", sqsMessage.ID),
		zap.String("message_group_id", groupId),
	)

	return sqsMessage, nil
}

// ReceiveMessage receives a message from the SQS FIFO queue and automatically deletes it
func (s *SQSService) ReceiveMessage(ctx context.Context) (*models.SQSMessage, error) {
	// Create context with timeout
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	// Prepare SQS receive message input
	input := &sqs.ReceiveMessageInput{
		QueueUrl:            aws.String(s.queueURL),
		MaxNumberOfMessages: 1,
		WaitTimeSeconds:     1, // Short polling for immediate response
		AttributeNames: []types.QueueAttributeName{
			types.QueueAttributeNameAll,
		},
	}

	// Receive message from SQS
	result, err := s.client.ReceiveMessage(ctx, input)
	if err != nil {
		s.logger.Error("Failed to receive message from SQS",
			zap.Error(err),
			zap.String("queue_url", s.queueURL),
		)
		return nil, fmt.Errorf("failed to receive message from SQS: %w", err)
	}

	// Check if any messages were received
	if len(result.Messages) == 0 {
		return nil, nil // No messages available
	}

	message := result.Messages[0]

	// Create SQS message
	sqsMessage := &models.SQSMessage{
		ID:            aws.ToString(message.MessageId),
		Body:          json.RawMessage(aws.ToString(message.Body)),
		ReceiptHandle: aws.ToString(message.ReceiptHandle),
		Timestamp:     time.Now(),
	}

	// Delete the message from the queue
	deleteInput := &sqs.DeleteMessageInput{
		QueueUrl:      aws.String(s.queueURL),
		ReceiptHandle: aws.String(sqsMessage.ReceiptHandle),
	}

	_, err = s.client.DeleteMessage(ctx, deleteInput)
	if err != nil {
		s.logger.Error("Failed to delete message from SQS",
			zap.Error(err),
			zap.String("message_id", sqsMessage.ID),
			zap.String("receipt_handle", sqsMessage.ReceiptHandle),
		)
		// Don't return error here as message was successfully received
		// Log the error and continue
	} else {
		s.logger.Info("Message received and deleted from SQS",
			zap.String("message_id", sqsMessage.ID),
		)
	}

	return sqsMessage, nil
}

// GenerateMessageIDs generates MessageGroupId and MessageDeduplicationId for FIFO queue operations
func (s *SQSService) GenerateMessageIDs() (groupId, deduplicationId string) {
	// Use configured message group ID for consistent FIFO ordering
	groupId = s.messageGroupID

	// Generate unique deduplication ID using UUID and timestamp
	deduplicationId = fmt.Sprintf("%s-%d", uuid.New().String(), time.Now().UnixNano())

	// Hash the deduplication ID to ensure it meets SQS requirements (max 128 chars)
	hash := sha256.Sum256([]byte(deduplicationId))
	deduplicationId = hex.EncodeToString(hash[:])[:32] // Use first 32 chars of hash

	return groupId, deduplicationId
}

// ValidateConnection validates the connection to the SQS queue
func (s *SQSService) ValidateConnection(ctx context.Context) error {
	// Create context with timeout
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	// Check if queue URL is configured
	if s.queueURL == "" {
		return fmt.Errorf("SQS queue URL is not configured")
	}

	// Try to get queue attributes to validate connection
	input := &sqs.GetQueueAttributesInput{
		QueueUrl: aws.String(s.queueURL),
		AttributeNames: []types.QueueAttributeName{
			types.QueueAttributeNameQueueArn,
			types.QueueAttributeNameFifoQueue,
		},
	}

	result, err := s.client.GetQueueAttributes(ctx, input)
	if err != nil {
		s.logger.Error("Failed to validate SQS connection",
			zap.Error(err),
			zap.String("queue_url", s.queueURL),
		)
		return fmt.Errorf("failed to validate SQS connection: %w", err)
	}

	// Verify it's a FIFO queue
	if fifoAttr, exists := result.Attributes[string(types.QueueAttributeNameFifoQueue)]; !exists || fifoAttr != "true" {
		return fmt.Errorf("queue is not a FIFO queue: %s", s.queueURL)
	}

	s.logger.Info("SQS connection validated successfully",
		zap.String("queue_url", s.queueURL),
		zap.String("queue_arn", result.Attributes[string(types.QueueAttributeNameQueueArn)]),
	)

	return nil
}
