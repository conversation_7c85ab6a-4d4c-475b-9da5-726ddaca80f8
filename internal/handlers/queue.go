package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"

	"gomad/internal/logger"
	"gomad/internal/models"
	"gomad/internal/services"
)

// QueueHandler handles queue-related HTTP requests
type QueueHandler struct {
	logger     *logger.Logger
	sqsService services.SQSServicer
}

// NewQueueHandler creates a new queue handler instance
func NewQueueHandler(logger *logger.Logger, sqsService services.SQSServicer) *QueueHandler {
	return &QueueHandler{
		logger:     logger,
		sqsService: sqsService,
	}
}

// HandlePostQueue handles POST /queue requests for JSON message submission to SQS
// Accepts JSON payloads and forwards them to SQS FIFO queue with proper deduplication
func (h *QueueHandler) HandlePostQueue(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	
	// Generate request ID for correlation
	requestID := fmt.Sprintf("req-%s", uuid.New().String()[:8])
	
	// Create logger with request ID
	reqLogger := h.logger.WithRequestID(requestID)

	// Validate HTTP method
	if r.Method != http.MethodPost {
		h.writeErrorResponse(w, reqLogger, requestID, http.StatusMethodNotAllowed, 
			"Method not allowed", "Only POST method is supported")
		return
	}

	// Validate Content-Type
	contentType := r.Header.Get("Content-Type")
	if !strings.HasPrefix(contentType, "application/json") {
		h.writeErrorResponse(w, reqLogger, requestID, http.StatusBadRequest,
			"Invalid content type", "Content-Type must be application/json")
		return
	}

	// Read and validate request body
	body, err := h.readAndValidateBody(r)
	if err != nil {
		h.writeErrorResponse(w, reqLogger, requestID, http.StatusBadRequest,
			"Invalid request body", err.Error())
		return
	}

	// Parse and validate JSON
	jsonPayload, err := h.parseAndValidateJSON(body)
	if err != nil {
		h.writeErrorResponse(w, reqLogger, requestID, http.StatusBadRequest,
			"Invalid JSON payload", err.Error())
		return
	}

	// Send message to SQS with timeout context
	ctx, cancel := context.WithTimeout(r.Context(), 5*time.Second)
	defer cancel()

	sqsMessage, err := h.sqsService.SendMessage(ctx, jsonPayload)
	if err != nil {
		reqLogger.Error("Failed to send message to SQS",
			zap.Error(err),
			zap.String("request_id", requestID),
		)
		h.writeErrorResponse(w, reqLogger, requestID, http.StatusInternalServerError,
			"Queue service unavailable", "Failed to queue message")
		return
	}

	// Create success response
	responseData := map[string]interface{}{
		"messageId":               sqsMessage.ID,
		"messageGroupId":          sqsMessage.MessageGroupId,
		"messageDeduplicationId":  sqsMessage.MessageDeduplicationId,
		"timestamp":               sqsMessage.Timestamp.Format(time.RFC3339),
	}

	response := models.NewSuccessResponse(
		requestID,
		"Message queued successfully",
		responseData,
	)

	// Write success response
	models.WriteJSONResponse(w, reqLogger.Logger, response, http.StatusOK)

	// Log the API request
	duration := time.Since(start)
	reqLogger.LogAPIRequest(r.Method, r.URL.Path, duration)
	
	reqLogger.Info("Message queued successfully",
		zap.String("message_id", sqsMessage.ID),
		zap.Duration("duration", duration),
	)
}

// readAndValidateBody reads and validates the HTTP request body
func (h *QueueHandler) readAndValidateBody(r *http.Request) ([]byte, error) {
	// Limit body size to prevent abuse (1MB limit)
	const maxBodySize = 1024 * 1024 // 1MB
	r.Body = http.MaxBytesReader(nil, r.Body, maxBodySize)

	// Read body
	body, err := io.ReadAll(r.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read request body: %w", err)
	}

	// Check for empty body
	if len(body) == 0 {
		return nil, fmt.Errorf("request body cannot be empty")
	}

	return body, nil
}

// parseAndValidateJSON parses and validates JSON payload
func (h *QueueHandler) parseAndValidateJSON(body []byte) (json.RawMessage, error) {
	// Validate JSON syntax
	var temp interface{}
	if err := json.Unmarshal(body, &temp); err != nil {
		return nil, fmt.Errorf("invalid JSON syntax: %w", err)
	}

	// Check for null payload
	if temp == nil {
		return nil, fmt.Errorf("JSON payload cannot be null")
	}

	// Check for empty object
	if obj, ok := temp.(map[string]interface{}); ok && len(obj) == 0 {
		return nil, fmt.Errorf("JSON payload cannot be empty object")
	}

	// Return as RawMessage for SQS forwarding
	return json.RawMessage(body), nil
}

// writeErrorResponse writes a standardized error response
func (h *QueueHandler) writeErrorResponse(w http.ResponseWriter, logger *logger.Logger, requestID string, statusCode int, message, errorDetail string) {
	response := models.NewErrorResponse(requestID, message, errorDetail)
	models.WriteJSONResponse(w, logger.Logger, response, statusCode)
	
	logger.Warn("Request failed",
		zap.String("request_id", requestID),
		zap.Int("status_code", statusCode),
		zap.String("error", errorDetail),
	)
}
