package handlers

import (
	"context"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gomad/internal/logger"
)

func TestRequestLoggingMiddleware(t *testing.T) {
	log, err := logger.NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	// Create test handler
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Verify request ID is in context
		requestID := GetRequestID(r.Context())
		assert.NotEmpty(t, requestID)
		assert.True(t, strings.HasPrefix(requestID, "req-"))

		// Verify logger is in context
		reqLogger := GetLogger(r.Context())
		assert.NotNil(t, reqLogger)

		w.WriteHeader(http.StatusOK)
		w.Write([]byte("test response"))
	})

	// Wrap with middleware
	middleware := RequestLoggingMiddleware(log)
	handler := middleware(testHandler)

	// Create test request
	req := httptest.NewRequest("GET", "/test", nil)
	req.Header.Set("User-Agent", "test-agent")
	w := httptest.NewRecorder()

	// Execute request
	start := time.Now()
	handler.ServeHTTP(w, req)
	duration := time.Since(start)

	// Verify response
	assert.Equal(t, http.StatusOK, w.Code)
	assert.Equal(t, "test response", w.Body.String())

	// Verify timing is reasonable
	assert.True(t, duration < 100*time.Millisecond)
}

func TestRequestLoggingMiddleware_ErrorStatus(t *testing.T) {
	log, err := logger.NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	// Create test handler that returns error
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusBadRequest)
		w.Write([]byte("error response"))
	})

	// Wrap with middleware
	middleware := RequestLoggingMiddleware(log)
	handler := middleware(testHandler)

	// Create test request
	req := httptest.NewRequest("POST", "/error", nil)
	w := httptest.NewRecorder()

	// Execute request
	handler.ServeHTTP(w, req)

	// Verify response
	assert.Equal(t, http.StatusBadRequest, w.Code)
	assert.Equal(t, "error response", w.Body.String())
}

func TestErrorHandlingMiddleware_Panic(t *testing.T) {
	log, err := logger.NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	// Create test handler that panics
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		panic("test panic")
	})

	// Wrap with both middlewares
	requestMiddleware := RequestLoggingMiddleware(log)
	errorMiddleware := ErrorHandlingMiddleware(log)
	handler := requestMiddleware(errorMiddleware(testHandler))

	// Create test request
	req := httptest.NewRequest("GET", "/panic", nil)
	w := httptest.NewRecorder()

	// Execute request (should not panic)
	handler.ServeHTTP(w, req)

	// Verify response
	assert.Equal(t, http.StatusInternalServerError, w.Code)
	assert.Contains(t, w.Body.String(), "Internal server error")
	assert.Contains(t, w.Body.String(), "req-") // Should contain request ID
}

func TestCORSMiddleware(t *testing.T) {
	// Create test handler
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("test"))
	})

	// Wrap with middleware
	middleware := CORSMiddleware()
	handler := middleware(testHandler)

	// Test regular request
	req := httptest.NewRequest("GET", "/test", nil)
	w := httptest.NewRecorder()

	handler.ServeHTTP(w, req)

	// Verify CORS headers
	assert.Equal(t, "*", w.Header().Get("Access-Control-Allow-Origin"))
	assert.Equal(t, "GET, POST, OPTIONS", w.Header().Get("Access-Control-Allow-Methods"))
	assert.Equal(t, "Content-Type, Authorization", w.Header().Get("Access-Control-Allow-Headers"))
	assert.Equal(t, http.StatusOK, w.Code)
}

func TestCORSMiddleware_Preflight(t *testing.T) {
	// Create test handler
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		t.Error("Handler should not be called for OPTIONS request")
	})

	// Wrap with middleware
	middleware := CORSMiddleware()
	handler := middleware(testHandler)

	// Test OPTIONS request
	req := httptest.NewRequest("OPTIONS", "/test", nil)
	w := httptest.NewRecorder()

	handler.ServeHTTP(w, req)

	// Verify preflight response
	assert.Equal(t, http.StatusOK, w.Code)
	assert.Equal(t, "*", w.Header().Get("Access-Control-Allow-Origin"))
}

func TestSecurityHeadersMiddleware(t *testing.T) {
	// Create test handler
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("test"))
	})

	// Wrap with middleware
	middleware := SecurityHeadersMiddleware()
	handler := middleware(testHandler)

	// Create test request
	req := httptest.NewRequest("GET", "/test", nil)
	w := httptest.NewRecorder()

	handler.ServeHTTP(w, req)

	// Verify security headers
	assert.Equal(t, "nosniff", w.Header().Get("X-Content-Type-Options"))
	assert.Equal(t, "DENY", w.Header().Get("X-Frame-Options"))
	assert.Equal(t, "1; mode=block", w.Header().Get("X-XSS-Protection"))
	assert.Equal(t, "strict-origin-when-cross-origin", w.Header().Get("Referrer-Policy"))
	assert.Equal(t, http.StatusOK, w.Code)
}

func TestGetRequestID_WithContext(t *testing.T) {
	requestID := "req-test123"
	ctx := context.WithValue(context.Background(), RequestIDKey, requestID)

	result := GetRequestID(ctx)
	assert.Equal(t, requestID, result)
}

func TestGetRequestID_WithoutContext(t *testing.T) {
	ctx := context.Background()

	result := GetRequestID(ctx)
	assert.Equal(t, "unknown", result)
}

func TestGetLogger_WithContext(t *testing.T) {
	log, err := logger.NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	ctx := context.WithValue(context.Background(), LoggerKey, log)

	result := GetLogger(ctx)
	assert.Equal(t, log, result)
}

func TestGetLogger_WithoutContext(t *testing.T) {
	ctx := context.Background()

	result := GetLogger(ctx)
	assert.NotNil(t, result) // Should return fallback logger
}

func TestResponseWriter_StatusCode(t *testing.T) {
	w := httptest.NewRecorder()
	rw := &responseWriter{
		ResponseWriter: w,
		statusCode:     http.StatusOK,
	}

	rw.WriteHeader(http.StatusBadRequest)
	assert.Equal(t, http.StatusBadRequest, rw.statusCode)
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestResponseWriter_BytesWritten(t *testing.T) {
	w := httptest.NewRecorder()
	rw := &responseWriter{
		ResponseWriter: w,
		statusCode:     http.StatusOK,
	}

	testData := []byte("test response data")
	n, err := rw.Write(testData)

	assert.NoError(t, err)
	assert.Equal(t, len(testData), n)
	assert.Equal(t, int64(len(testData)), rw.bytesWritten)
	assert.Equal(t, string(testData), w.Body.String())
}

func TestMiddlewareChain(t *testing.T) {
	log, err := logger.NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	// Create test handler
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Verify all middleware context is available
		requestID := GetRequestID(r.Context())
		assert.NotEmpty(t, requestID)

		reqLogger := GetLogger(r.Context())
		assert.NotNil(t, reqLogger)

		w.WriteHeader(http.StatusOK)
		w.Write([]byte("success"))
	})

	// Chain all middleware
	handler := SecurityHeadersMiddleware()(
		CORSMiddleware()(
			RequestLoggingMiddleware(log)(
				ErrorHandlingMiddleware(log)(testHandler),
			),
		),
	)

	// Create test request
	req := httptest.NewRequest("GET", "/test", nil)
	w := httptest.NewRecorder()

	// Execute request
	handler.ServeHTTP(w, req)

	// Verify response
	assert.Equal(t, http.StatusOK, w.Code)
	assert.Equal(t, "success", w.Body.String())

	// Verify all headers are set
	assert.NotEmpty(t, w.Header().Get("Access-Control-Allow-Origin"))
	assert.NotEmpty(t, w.Header().Get("X-Content-Type-Options"))
}
