package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"

	"gomad/internal/logger"
	"gomad/internal/models"
	"gomad/internal/services"
)

// ReadHandler handles message retrieval HTTP requests
type ReadHandler struct {
	logger     *logger.Logger
	sqsService services.SQSServicer
}

// NewReadHandler creates a new read handler instance
func NewReadHandler(logger *logger.Logger, sqsService services.SQSServicer) *ReadHandler {
	return &ReadHandler{
		logger:     logger,
		sqsService: sqsService,
	}
}

// HandleGetRead handles GET /read requests for message retrieval from SQS FIFO queue
// Retrieves the first available message in FIFO order and automatically deletes it from the queue
func (h *ReadHandler) HandleGetRead(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	
	// Generate request ID for correlation
	requestID := fmt.Sprintf("req-%s", uuid.New().String()[:8])
	
	// Create logger with request ID
	reqLogger := h.logger.WithRequestID(requestID)

	// Validate HTTP method
	if r.Method != http.MethodGet {
		h.writeErrorResponse(w, reqLogger, requestID, http.StatusMethodNotAllowed, 
			"Method not allowed", "Only GET method is supported")
		return
	}

	// Retrieve message from SQS with timeout context
	ctx, cancel := context.WithTimeout(r.Context(), 5*time.Second)
	defer cancel()

	sqsMessage, err := h.sqsService.ReceiveMessage(ctx)
	if err != nil {
		reqLogger.Error("Failed to receive message from SQS",
			zap.Error(err),
			zap.String("request_id", requestID),
		)
		h.writeErrorResponse(w, reqLogger, requestID, http.StatusInternalServerError,
			"Queue service unavailable", "Failed to receive messages from SQS")
		return
	}

	// Handle empty queue (no messages available)
	if sqsMessage == nil {
		// Return HTTP 204 No Content for empty queue
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusNoContent)
		
		// Log the API request
		duration := time.Since(start)
		reqLogger.LogAPIRequest(r.Method, r.URL.Path, duration)
		
		reqLogger.Info("No messages available in queue",
			zap.Duration("duration", duration),
		)
		return
	}

	// Parse message body to ensure it's valid JSON
	var messageBody interface{}
	if err := json.Unmarshal(sqsMessage.Body, &messageBody); err != nil {
		reqLogger.Error("Failed to parse message body as JSON",
			zap.Error(err),
			zap.String("message_id", sqsMessage.ID),
			zap.String("request_id", requestID),
		)
		h.writeErrorResponse(w, reqLogger, requestID, http.StatusInternalServerError,
			"Message format error", "Failed to parse message content")
		return
	}

	// Create success response with message data
	responseData := map[string]interface{}{
		"messageId": sqsMessage.ID,
		"body":      messageBody,
		"timestamp": sqsMessage.Timestamp.Format(time.RFC3339),
	}

	response := models.NewSuccessResponse(
		requestID,
		"Message retrieved successfully",
		responseData,
	)

	// Write success response
	models.WriteJSONResponse(w, reqLogger.Logger, response, http.StatusOK)

	// Log the API request
	duration := time.Since(start)
	reqLogger.LogAPIRequest(r.Method, r.URL.Path, duration)
	
	reqLogger.Info("Message retrieved successfully",
		zap.String("message_id", sqsMessage.ID),
		zap.Duration("duration", duration),
	)
}

// writeErrorResponse writes a standardized error response
func (h *ReadHandler) writeErrorResponse(w http.ResponseWriter, logger *logger.Logger, requestID string, statusCode int, message, errorDetail string) {
	response := models.NewErrorResponse(requestID, message, errorDetail)
	models.WriteJSONResponse(w, logger.Logger, response, statusCode)
	
	logger.Warn("Request failed",
		zap.String("request_id", requestID),
		zap.Int("status_code", statusCode),
		zap.String("error", errorDetail),
	)
}
