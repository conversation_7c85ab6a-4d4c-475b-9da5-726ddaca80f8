package handlers

import (
	"fmt"
	"net/http"
	"time"

	"gomad/internal/logger"
	"gomad/internal/models"

	"github.com/google/uuid"
)

// HealthHandler handles health check requests
type HealthHandler struct {
	logger    *logger.Logger
	startTime time.Time
	version   string
}

// NewHealthHandler creates a new health check handler
func NewHealthHandler(logger *logger.Logger, version string) *HealthHandler {
	return &HealthHandler{
		logger:    logger,
		startTime: time.Now(),
		version:   version,
	}
}

// HandleGetHealth handles GET /health requests
// Returns HTTP 200 with JSON response indicating service status
func (h *HealthHandler) HandleGetHealth(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	
	// Generate request ID for correlation
	requestID := fmt.Sprintf("req-%s", uuid.New().String()[:8])
	
	// Create logger with request ID
	reqLogger := h.logger.WithRequestID(requestID)

	// Calculate uptime
	uptime := time.Since(h.startTime)
	uptimeStr := formatUptime(uptime)

	// Create health data
	healthData := &models.HealthData{
		Status:  "healthy",
		Uptime:  uptimeStr,
		Version: h.version,
	}

	// Create success response
	response := models.NewSuccessResponse(
		requestID,
		"Service is healthy",
		healthData,
	)

	// Write JSON response
	models.WriteJSONResponse(w, reqLogger.Logger, response, http.StatusOK)

	// Log the API request
	duration := time.Since(start)
	reqLogger.LogAPIRequest(r.Method, r.URL.Path, duration)
}

// formatUptime formats a duration into a human-readable string
func formatUptime(d time.Duration) string {
	if d < time.Minute {
		return fmt.Sprintf("%.0fs", d.Seconds())
	}
	
	hours := int(d.Hours())
	minutes := int(d.Minutes()) % 60
	seconds := int(d.Seconds()) % 60

	if hours > 0 {
		return fmt.Sprintf("%dh%dm%ds", hours, minutes, seconds)
	}
	
	return fmt.Sprintf("%dm%ds", minutes, seconds)
}
