package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gomad/internal/logger"
	"gomad/internal/models"
	"gomad/test/mocks"
)

func TestNewReadHandler(t *testing.T) {
	log, err := logger.NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	mockSQS := &mocks.MockSQSService{}
	handler := NewReadHandler(log, mockSQS)

	assert.NotNil(t, handler)
	assert.Equal(t, log, handler.logger)
	assert.Equal(t, mockSQS, handler.sqsService)
}

func TestHandleGetRead_Success(t *testing.T) {
	log, err := logger.NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	// Create test message
	testPayload := map[string]interface{}{
		"orderId":    "ORD-12345",
		"customerId": "CUST-67890",
		"amount":     99.99,
	}
	payloadBytes, err := json.Marshal(testPayload)
	require.NoError(t, err)

	// Create mock SQS service
	mockSQS := &mocks.MockSQSService{
		ReceiveMessageFunc: func(ctx context.Context) (*models.SQSMessage, error) {
			return &models.SQSMessage{
				ID:            "test-message-id",
				Body:          json.RawMessage(payloadBytes),
				Timestamp:     time.Now(),
				ReceiptHandle: "test-receipt-handle",
			}, nil
		},
	}

	handler := NewReadHandler(log, mockSQS)

	// Create test request
	req := httptest.NewRequest("GET", "/read", nil)
	w := httptest.NewRecorder()

	// Call handler
	start := time.Now()
	handler.HandleGetRead(w, req)
	duration := time.Since(start)

	// Verify response time requirement (under 100ms)
	assert.True(t, duration < 100*time.Millisecond, "Response time should be under 100ms, got %v", duration)

	// Verify response
	assert.Equal(t, http.StatusOK, w.Code)
	assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

	// Parse response
	var response models.APIResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	// Verify response structure
	assert.True(t, response.Success)
	assert.Equal(t, "Message retrieved successfully", response.Message)
	assert.NotEmpty(t, response.RequestID)
	assert.NotNil(t, response.Data)

	// Verify response data
	responseData, ok := response.Data.(map[string]interface{})
	require.True(t, ok)
	assert.Equal(t, "test-message-id", responseData["messageId"])
	assert.NotEmpty(t, responseData["timestamp"])

	// Verify message body
	body, ok := responseData["body"].(map[string]interface{})
	require.True(t, ok)
	assert.Equal(t, "ORD-12345", body["orderId"])
	assert.Equal(t, "CUST-67890", body["customerId"])
	assert.Equal(t, 99.99, body["amount"])
}

func TestHandleGetRead_EmptyQueue(t *testing.T) {
	log, err := logger.NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	// Create mock SQS service that returns no messages
	mockSQS := &mocks.MockSQSService{
		ReceiveMessageFunc: func(ctx context.Context) (*models.SQSMessage, error) {
			return nil, nil // No messages available
		},
	}

	handler := NewReadHandler(log, mockSQS)

	// Create test request
	req := httptest.NewRequest("GET", "/read", nil)
	w := httptest.NewRecorder()

	// Call handler
	handler.HandleGetRead(w, req)

	// Verify response
	assert.Equal(t, http.StatusNoContent, w.Code)
	assert.Equal(t, "application/json", w.Header().Get("Content-Type"))
	assert.Empty(t, w.Body.String()) // No Content response should have empty body
}

func TestHandleGetRead_InvalidMethod(t *testing.T) {
	log, err := logger.NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	mockSQS := &mocks.MockSQSService{}
	handler := NewReadHandler(log, mockSQS)

	// Test POST method (should fail)
	req := httptest.NewRequest("POST", "/read", nil)
	w := httptest.NewRecorder()

	handler.HandleGetRead(w, req)

	assert.Equal(t, http.StatusMethodNotAllowed, w.Code)

	var response models.APIResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.False(t, response.Success)
	assert.Equal(t, "Method not allowed", response.Message)
	assert.NotEmpty(t, response.RequestID)
}

func TestHandleGetRead_SQSError(t *testing.T) {
	log, err := logger.NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	// Create mock SQS service that returns error
	mockSQS := &mocks.MockSQSService{
		ReceiveMessageFunc: func(ctx context.Context) (*models.SQSMessage, error) {
			return nil, fmt.Errorf("SQS service unavailable")
		},
	}

	handler := NewReadHandler(log, mockSQS)

	// Create test request
	req := httptest.NewRequest("GET", "/read", nil)
	w := httptest.NewRecorder()

	handler.HandleGetRead(w, req)

	assert.Equal(t, http.StatusInternalServerError, w.Code)

	var response models.APIResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.False(t, response.Success)
	assert.Equal(t, "Queue service unavailable", response.Message)
	assert.Equal(t, "Failed to receive messages from SQS", response.Error)
	assert.NotEmpty(t, response.RequestID)
}

func TestHandleGetRead_InvalidMessageBody(t *testing.T) {
	log, err := logger.NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	// Create mock SQS service with invalid JSON message
	mockSQS := &mocks.MockSQSService{
		ReceiveMessageFunc: func(ctx context.Context) (*models.SQSMessage, error) {
			return &models.SQSMessage{
				ID:            "test-message-id",
				Body:          json.RawMessage(`{"invalid": json`), // Malformed JSON
				Timestamp:     time.Now(),
				ReceiptHandle: "test-receipt-handle",
			}, nil
		},
	}

	handler := NewReadHandler(log, mockSQS)

	// Create test request
	req := httptest.NewRequest("GET", "/read", nil)
	w := httptest.NewRecorder()

	handler.HandleGetRead(w, req)

	assert.Equal(t, http.StatusInternalServerError, w.Code)

	var response models.APIResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.False(t, response.Success)
	assert.Equal(t, "Message format error", response.Message)
	assert.Equal(t, "Failed to parse message content", response.Error)
	assert.NotEmpty(t, response.RequestID)
}

func TestHandleGetRead_ComplexMessage(t *testing.T) {
	log, err := logger.NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	// Create complex test message
	testPayload := map[string]interface{}{
		"eventType": "order_fulfillment",
		"orderId":   "ORD-99999",
		"fulfillment": map[string]interface{}{
			"warehouseId": "WH-002",
			"items": []map[string]interface{}{
				{
					"productId": "PROD-003",
					"quantity":  1,
					"location": map[string]interface{}{
						"aisle": "A",
						"shelf": "12",
						"bin":   "C",
					},
				},
			},
		},
		"timestamp": "2025-08-31T11:00:00Z",
	}
	payloadBytes, err := json.Marshal(testPayload)
	require.NoError(t, err)

	// Create mock SQS service
	mockSQS := &mocks.MockSQSService{
		ReceiveMessageFunc: func(ctx context.Context) (*models.SQSMessage, error) {
			return &models.SQSMessage{
				ID:            "complex-message-id",
				Body:          json.RawMessage(payloadBytes),
				Timestamp:     time.Now(),
				ReceiptHandle: "complex-receipt-handle",
			}, nil
		},
	}

	handler := NewReadHandler(log, mockSQS)

	// Create test request
	req := httptest.NewRequest("GET", "/read", nil)
	w := httptest.NewRecorder()

	// Call handler
	handler.HandleGetRead(w, req)

	// Verify response
	assert.Equal(t, http.StatusOK, w.Code)

	// Parse response
	var response models.APIResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	// Verify response structure
	assert.True(t, response.Success)
	assert.Equal(t, "Message retrieved successfully", response.Message)

	// Verify complex message body structure is preserved
	responseData, ok := response.Data.(map[string]interface{})
	require.True(t, ok)

	body, ok := responseData["body"].(map[string]interface{})
	require.True(t, ok)

	assert.Equal(t, "order_fulfillment", body["eventType"])
	assert.Equal(t, "ORD-99999", body["orderId"])

	fulfillment, ok := body["fulfillment"].(map[string]interface{})
	require.True(t, ok)
	assert.Equal(t, "WH-002", fulfillment["warehouseId"])

	items, ok := fulfillment["items"].([]interface{})
	require.True(t, ok)
	assert.Len(t, items, 1)
}

func TestHandleGetRead_ConcurrentRequests(t *testing.T) {
	log, err := logger.NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	// Create test message
	testPayload := map[string]interface{}{
		"orderId":    "ORD-12345",
		"customerId": "CUST-67890",
		"amount":     99.99,
	}
	payloadBytes, err := json.Marshal(testPayload)
	require.NoError(t, err)

	// Create mock SQS service
	mockSQS := &mocks.MockSQSService{
		ReceiveMessageFunc: func(ctx context.Context) (*models.SQSMessage, error) {
			// Simulate some processing time
			time.Sleep(1 * time.Millisecond)
			return &models.SQSMessage{
				ID:            "test-message-id",
				Body:          json.RawMessage(payloadBytes),
				Timestamp:     time.Now(),
				ReceiptHandle: "test-receipt-handle",
			}, nil
		},
	}

	handler := NewReadHandler(log, mockSQS)

	// Test concurrent requests
	const numRequests = 100
	const concurrency = 50

	// Channel to collect results
	results := make(chan error, numRequests)

	// Semaphore to limit concurrency
	sem := make(chan struct{}, concurrency)

	// Launch concurrent requests
	start := time.Now()
	for i := 0; i < numRequests; i++ {
		go func(requestID int) {
			sem <- struct{}{}        // Acquire semaphore
			defer func() { <-sem }() // Release semaphore

			// Create request
			req := httptest.NewRequest("GET", "/read", nil)
			w := httptest.NewRecorder()

			// Call handler
			handler.HandleGetRead(w, req)

			// Check response
			if w.Code != http.StatusOK {
				results <- fmt.Errorf("request %d failed with status %d", requestID, w.Code)
				return
			}

			results <- nil
		}(i)
	}

	// Collect results
	for i := 0; i < numRequests; i++ {
		err := <-results
		assert.NoError(t, err)
	}

	duration := time.Since(start)
	t.Logf("Processed %d concurrent read requests in %v", numRequests, duration)

	// Should handle 100 requests in reasonable time (under 1 second)
	assert.True(t, duration < 1*time.Second, "Should handle concurrent requests efficiently")
}
