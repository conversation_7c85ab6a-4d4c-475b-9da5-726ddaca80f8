package handlers

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"gomad/internal/logger"
	"gomad/internal/models"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewHealthHandler(t *testing.T) {
	log, err := logger.NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	version := "1.0.0"
	handler := NewHealthHandler(log, version)

	assert.NotNil(t, handler)
	assert.Equal(t, log, handler.logger)
	assert.Equal(t, version, handler.version)
	assert.True(t, time.Since(handler.startTime) < time.Second) // Should be very recent
}

func TestHandleGetHealth_Success(t *testing.T) {
	log, err := logger.NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	handler := NewHealthHandler(log, "1.0.0")

	// Create test request
	req := httptest.NewRequest("GET", "/health", nil)
	w := httptest.NewRecorder()

	// Call handler
	start := time.Now()
	handler.HandleGetHealth(w, req)
	duration := time.Since(start)

	// Verify response time requirement (under 10ms)
	assert.True(t, duration < 10*time.Millisecond, "Response time should be under 10ms, got %v", duration)

	// Verify HTTP status
	assert.Equal(t, http.StatusOK, w.Code)
	assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

	// Parse response
	var response models.APIResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	// Verify APIResponse structure
	assert.True(t, response.Success)
	assert.Equal(t, "Service is healthy", response.Message)
	assert.NotEmpty(t, response.RequestID)
	assert.Empty(t, response.Error)

	// Verify health data
	require.NotNil(t, response.Data)
	healthDataBytes, err := json.Marshal(response.Data)
	require.NoError(t, err)

	var healthData models.HealthData
	err = json.Unmarshal(healthDataBytes, &healthData)
	require.NoError(t, err)

	assert.Equal(t, "healthy", healthData.Status)
	assert.Equal(t, "1.0.0", healthData.Version)
	assert.NotEmpty(t, healthData.Uptime)
}

func TestHandleGetHealth_UptimeFormat(t *testing.T) {
	log, err := logger.NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	handler := NewHealthHandler(log, "1.0.0")
	
	// Simulate some uptime by setting start time in the past
	handler.startTime = time.Now().Add(-2*time.Hour - 30*time.Minute - 45*time.Second)

	req := httptest.NewRequest("GET", "/health", nil)
	w := httptest.NewRecorder()

	handler.HandleGetHealth(w, req)

	var response models.APIResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	healthDataBytes, err := json.Marshal(response.Data)
	require.NoError(t, err)

	var healthData models.HealthData
	err = json.Unmarshal(healthDataBytes, &healthData)
	require.NoError(t, err)

	// Uptime should be in format like "2h30m45s"
	assert.Contains(t, healthData.Uptime, "h")
	assert.Contains(t, healthData.Uptime, "m")
	assert.Contains(t, healthData.Uptime, "s")
}

func TestFormatUptime(t *testing.T) {
	tests := []struct {
		name     string
		duration time.Duration
		expected string
	}{
		{"seconds only", 45 * time.Second, "45s"},
		{"minutes and seconds", 2*time.Minute + 30*time.Second, "2m30s"},
		{"hours, minutes, seconds", 2*time.Hour + 30*time.Minute + 45*time.Second, "2h30m45s"},
		{"exactly one hour", 1 * time.Hour, "1h0m0s"},
		{"exactly one minute", 1 * time.Minute, "1m0s"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := formatUptime(tt.duration)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestHandleGetHealth_RequestIDGeneration(t *testing.T) {
	log, err := logger.NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	handler := NewHealthHandler(log, "1.0.0")

	// Make multiple requests
	requestIDs := make(map[string]bool)
	for i := 0; i < 5; i++ {
		req := httptest.NewRequest("GET", "/health", nil)
		w := httptest.NewRecorder()

		handler.HandleGetHealth(w, req)

		var response models.APIResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		// Verify request ID format (should start with "req-")
		assert.True(t, len(response.RequestID) > 4)
		assert.Equal(t, "req-", response.RequestID[:4])

		// Verify uniqueness
		assert.False(t, requestIDs[response.RequestID], "Request ID should be unique")
		requestIDs[response.RequestID] = true
	}
}
