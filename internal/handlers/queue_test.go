package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gomad/internal/logger"
	"gomad/internal/models"
	"gomad/test/mocks"
)

func TestNewQueueHandler(t *testing.T) {
	log, err := logger.NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	mockSQS := &mocks.MockSQSService{}
	handler := NewQueueHandler(log, mockSQS)

	assert.NotNil(t, handler)
	assert.Equal(t, log, handler.logger)
	assert.Equal(t, mockSQS, handler.sqsService)
}

func TestHandlePostQueue_Success(t *testing.T) {
	log, err := logger.NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	// Create mock SQS service
	mockSQS := &mocks.MockSQSService{
		SendMessageFunc: func(ctx context.Context, body json.RawMessage) (*models.SQSMessage, error) {
			return &models.SQSMessage{
				ID:                     "test-message-id",
				Body:                   body,
				MessageGroupId:         "test-group",
				MessageDeduplicationId: "test-dedup-id",
				Timestamp:              time.Now(),
			}, nil
		},
	}

	handler := NewQueueHandler(log, mockSQS)

	// Create test request
	testPayload := map[string]interface{}{
		"orderId":    "ORD-12345",
		"customerId": "CUST-67890",
		"amount":     99.99,
	}
	payloadBytes, err := json.Marshal(testPayload)
	require.NoError(t, err)

	req := httptest.NewRequest("POST", "/queue", bytes.NewReader(payloadBytes))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	// Call handler
	start := time.Now()
	handler.HandlePostQueue(w, req)
	duration := time.Since(start)

	// Verify response time requirement (under 100ms)
	assert.True(t, duration < 100*time.Millisecond, "Response time should be under 100ms, got %v", duration)

	// Verify response
	assert.Equal(t, http.StatusOK, w.Code)
	assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

	// Parse response
	var response models.APIResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	// Verify response structure
	assert.True(t, response.Success)
	assert.Equal(t, "Message queued successfully", response.Message)
	assert.NotEmpty(t, response.RequestID)
	assert.NotNil(t, response.Data)

	// Verify response data
	responseData, ok := response.Data.(map[string]interface{})
	require.True(t, ok)
	assert.Equal(t, "test-message-id", responseData["messageId"])
	assert.Equal(t, "test-group", responseData["messageGroupId"])
	assert.Equal(t, "test-dedup-id", responseData["messageDeduplicationId"])
	assert.NotEmpty(t, responseData["timestamp"])
}

func TestHandlePostQueue_InvalidMethod(t *testing.T) {
	log, err := logger.NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	mockSQS := &mocks.MockSQSService{}
	handler := NewQueueHandler(log, mockSQS)

	// Test GET method (should fail)
	req := httptest.NewRequest("GET", "/queue", nil)
	w := httptest.NewRecorder()

	handler.HandlePostQueue(w, req)

	assert.Equal(t, http.StatusMethodNotAllowed, w.Code)

	var response models.APIResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.False(t, response.Success)
	assert.Equal(t, "Method not allowed", response.Message)
	assert.NotEmpty(t, response.RequestID)
}

func TestHandlePostQueue_InvalidContentType(t *testing.T) {
	log, err := logger.NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	mockSQS := &mocks.MockSQSService{}
	handler := NewQueueHandler(log, mockSQS)

	// Test with text/plain content type
	req := httptest.NewRequest("POST", "/queue", strings.NewReader("test"))
	req.Header.Set("Content-Type", "text/plain")
	w := httptest.NewRecorder()

	handler.HandlePostQueue(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response models.APIResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.False(t, response.Success)
	assert.Equal(t, "Invalid content type", response.Message)
	assert.Contains(t, response.Error, "Content-Type must be application/json")
}

func TestHandlePostQueue_EmptyBody(t *testing.T) {
	log, err := logger.NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	mockSQS := &mocks.MockSQSService{}
	handler := NewQueueHandler(log, mockSQS)

	// Test with empty body
	req := httptest.NewRequest("POST", "/queue", bytes.NewReader([]byte{}))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	handler.HandlePostQueue(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response models.APIResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.False(t, response.Success)
	assert.Equal(t, "Invalid request body", response.Message)
	assert.Contains(t, response.Error, "cannot be empty")
}

func TestHandlePostQueue_InvalidJSON(t *testing.T) {
	log, err := logger.NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	mockSQS := &mocks.MockSQSService{}
	handler := NewQueueHandler(log, mockSQS)

	// Test with malformed JSON
	req := httptest.NewRequest("POST", "/queue", strings.NewReader(`{"invalid": json`))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	handler.HandlePostQueue(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response models.APIResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.False(t, response.Success)
	assert.Equal(t, "Invalid JSON payload", response.Message)
	assert.Contains(t, response.Error, "invalid JSON syntax")
}

func TestHandlePostQueue_NullPayload(t *testing.T) {
	log, err := logger.NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	mockSQS := &mocks.MockSQSService{}
	handler := NewQueueHandler(log, mockSQS)

	// Test with null JSON
	req := httptest.NewRequest("POST", "/queue", strings.NewReader("null"))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	handler.HandlePostQueue(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response models.APIResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.False(t, response.Success)
	assert.Equal(t, "Invalid JSON payload", response.Message)
	assert.Contains(t, response.Error, "cannot be null")
}

func TestHandlePostQueue_EmptyObject(t *testing.T) {
	log, err := logger.NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	mockSQS := &mocks.MockSQSService{}
	handler := NewQueueHandler(log, mockSQS)

	// Test with empty JSON object
	req := httptest.NewRequest("POST", "/queue", strings.NewReader("{}"))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	handler.HandlePostQueue(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response models.APIResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.False(t, response.Success)
	assert.Equal(t, "Invalid JSON payload", response.Message)
	assert.Contains(t, response.Error, "cannot be empty object")
}

func TestHandlePostQueue_SQSError(t *testing.T) {
	log, err := logger.NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	// Create mock SQS service that returns error
	mockSQS := &mocks.MockSQSService{
		SendMessageFunc: func(ctx context.Context, body json.RawMessage) (*models.SQSMessage, error) {
			return nil, fmt.Errorf("SQS service unavailable")
		},
	}

	handler := NewQueueHandler(log, mockSQS)

	// Create test request
	testPayload := map[string]interface{}{"test": "data"}
	payloadBytes, err := json.Marshal(testPayload)
	require.NoError(t, err)

	req := httptest.NewRequest("POST", "/queue", bytes.NewReader(payloadBytes))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	handler.HandlePostQueue(w, req)

	assert.Equal(t, http.StatusInternalServerError, w.Code)

	var response models.APIResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.False(t, response.Success)
	assert.Equal(t, "Queue service unavailable", response.Message)
	assert.Equal(t, "Failed to queue message", response.Error)
	assert.NotEmpty(t, response.RequestID)
}

func TestHandlePostQueue_LargePayload(t *testing.T) {
	log, err := logger.NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	mockSQS := &mocks.MockSQSService{}
	handler := NewQueueHandler(log, mockSQS)

	// Create payload larger than 1MB
	largeData := strings.Repeat("a", 1024*1024+1) // 1MB + 1 byte
	largePayload := map[string]string{"data": largeData}
	payloadBytes, err := json.Marshal(largePayload)
	require.NoError(t, err)

	req := httptest.NewRequest("POST", "/queue", bytes.NewReader(payloadBytes))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	handler.HandlePostQueue(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response models.APIResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.False(t, response.Success)
	assert.Equal(t, "Invalid request body", response.Message)
}

func TestHandlePostQueue_ConcurrentRequests(t *testing.T) {
	log, err := logger.NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	// Create mock SQS service
	mockSQS := &mocks.MockSQSService{
		SendMessageFunc: func(ctx context.Context, body json.RawMessage) (*models.SQSMessage, error) {
			// Simulate some processing time
			time.Sleep(1 * time.Millisecond)
			return &models.SQSMessage{
				ID:                     "test-message-id",
				Body:                   body,
				MessageGroupId:         "test-group",
				MessageDeduplicationId: "test-dedup-id",
				Timestamp:              time.Now(),
			}, nil
		},
	}

	handler := NewQueueHandler(log, mockSQS)

	// Test concurrent requests
	const numRequests = 100
	const concurrency = 50

	// Channel to collect results
	results := make(chan error, numRequests)

	// Semaphore to limit concurrency
	sem := make(chan struct{}, concurrency)

	// Test payload
	testPayload := map[string]interface{}{
		"orderId":    "ORD-12345",
		"customerId": "CUST-67890",
		"amount":     99.99,
	}
	payloadBytes, err := json.Marshal(testPayload)
	require.NoError(t, err)

	// Launch concurrent requests
	start := time.Now()
	for i := 0; i < numRequests; i++ {
		go func(requestID int) {
			sem <- struct{}{}        // Acquire semaphore
			defer func() { <-sem }() // Release semaphore

			// Create request
			req := httptest.NewRequest("POST", "/queue", bytes.NewReader(payloadBytes))
			req.Header.Set("Content-Type", "application/json")
			w := httptest.NewRecorder()

			// Call handler
			handler.HandlePostQueue(w, req)

			// Check response
			if w.Code != http.StatusOK {
				results <- fmt.Errorf("request %d failed with status %d", requestID, w.Code)
				return
			}

			results <- nil
		}(i)
	}

	// Collect results
	for i := 0; i < numRequests; i++ {
		err := <-results
		assert.NoError(t, err)
	}

	duration := time.Since(start)
	t.Logf("Processed %d concurrent requests in %v", numRequests, duration)

	// Should handle 100 requests in reasonable time (under 1 second)
	assert.True(t, duration < 1*time.Second, "Should handle concurrent requests efficiently")
}
