package handlers

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"

	"gomad/internal/logger"
	"gomad/internal/models"
)

// ContextKey type for context keys to avoid collisions
type ContextKey string

const (
	// RequestIDKey is the context key for request ID
	RequestIDKey ContextKey = "requestID"
	// LoggerKey is the context key for logger with request ID
	LoggerKey ContextKey = "logger"
)

// RequestLoggingMiddleware adds request correlation ID and logging to all HTTP requests
func RequestLoggingMiddleware(logger *logger.Logger) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			start := time.Now()
			
			// Generate request ID
			requestID := fmt.Sprintf("req-%s", uuid.New().String()[:8])
			
			// Create logger with request ID
			reqLogger := logger.WithRequestID(requestID)
			
			// Add request ID and logger to context
			ctx := context.WithValue(r.Context(), Request<PERSON><PERSON><PERSON>, requestID)
			ctx = context.WithValue(ctx, <PERSON><PERSON><PERSON><PERSON>, reqLogger)
			r = r.WithContext(ctx)
			
			// Create response writer wrapper to capture status code
			rw := &responseWriter{
				ResponseWriter: w,
				statusCode:     http.StatusOK,
			}
			
			// Log request start
			reqLogger.Info("Request started",
				zap.String("method", r.Method),
				zap.String("path", r.URL.Path),
				zap.String("remote_addr", r.RemoteAddr),
				zap.String("user_agent", r.UserAgent()),
			)
			
			// Call next handler
			next.ServeHTTP(rw, r)
			
			// Calculate duration
			duration := time.Since(start)
			
			// Log request completion
			logLevel := zap.InfoLevel
			if rw.statusCode >= 400 {
				logLevel = zap.WarnLevel
			}
			if rw.statusCode >= 500 {
				logLevel = zap.ErrorLevel
			}
			
			reqLogger.Log(logLevel, "Request completed",
				zap.String("method", r.Method),
				zap.String("path", r.URL.Path),
				zap.Int("status_code", rw.statusCode),
				zap.Duration("duration", duration),
				zap.Int64("response_size", rw.bytesWritten),
			)
			
			// Log API request for metrics
			reqLogger.LogAPIRequest(r.Method, r.URL.Path, duration)
		})
	}
}

// ErrorHandlingMiddleware provides consistent error response formatting
func ErrorHandlingMiddleware(logger *logger.Logger) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Recover from panics and convert to 500 errors
			defer func() {
				if err := recover(); err != nil {
					requestID := GetRequestID(r.Context())
					reqLogger := GetLogger(r.Context())
					
					reqLogger.Error("Panic recovered",
						zap.Any("panic", err),
						zap.String("request_id", requestID),
					)
					
					response := models.NewErrorResponse(
						requestID,
						"Internal server error",
						"An unexpected error occurred",
					)
					
					models.WriteJSONResponse(w, reqLogger.Logger, response, http.StatusInternalServerError)
				}
			}()
			
			next.ServeHTTP(w, r)
		})
	}
}

// CORSMiddleware adds CORS headers for API access
func CORSMiddleware() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Set CORS headers
			w.Header().Set("Access-Control-Allow-Origin", "*")
			w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
			w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
			
			// Handle preflight requests
			if r.Method == http.MethodOptions {
				w.WriteHeader(http.StatusOK)
				return
			}
			
			next.ServeHTTP(w, r)
		})
	}
}

// SecurityHeadersMiddleware adds security headers
func SecurityHeadersMiddleware() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Security headers
			w.Header().Set("X-Content-Type-Options", "nosniff")
			w.Header().Set("X-Frame-Options", "DENY")
			w.Header().Set("X-XSS-Protection", "1; mode=block")
			w.Header().Set("Referrer-Policy", "strict-origin-when-cross-origin")
			
			next.ServeHTTP(w, r)
		})
	}
}

// GetRequestID extracts request ID from context
func GetRequestID(ctx context.Context) string {
	if requestID, ok := ctx.Value(RequestIDKey).(string); ok {
		return requestID
	}
	return "unknown"
}

// GetLogger extracts logger with request ID from context
func GetLogger(ctx context.Context) *logger.Logger {
	if logger, ok := ctx.Value(LoggerKey).(*logger.Logger); ok {
		return logger
	}
	// Fallback to a basic logger if not found
	log, _ := logger.NewLogger("info")
	return log
}

// responseWriter wraps http.ResponseWriter to capture status code and response size
type responseWriter struct {
	http.ResponseWriter
	statusCode    int
	bytesWritten  int64
}

// WriteHeader captures the status code
func (rw *responseWriter) WriteHeader(code int) {
	rw.statusCode = code
	rw.ResponseWriter.WriteHeader(code)
}

// Write captures the number of bytes written
func (rw *responseWriter) Write(b []byte) (int, error) {
	n, err := rw.ResponseWriter.Write(b)
	rw.bytesWritten += int64(n)
	return n, err
}
