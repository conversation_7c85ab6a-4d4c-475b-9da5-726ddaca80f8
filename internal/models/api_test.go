package models

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
)

func TestNewSuccessResponse(t *testing.T) {
	requestID := "test-req-123"
	message := "Operation successful"
	data := map[string]string{"key": "value"}

	response := NewSuccessResponse(requestID, message, data)

	assert.True(t, response.Success)
	assert.Equal(t, message, response.Message)
	assert.Equal(t, data, response.Data)
	assert.Equal(t, requestID, response.RequestID)
	assert.Empty(t, response.Error)
}

func TestNewErrorResponse(t *testing.T) {
	requestID := "test-req-456"
	message := "Operation failed"
	errorDetail := "Detailed error information"

	response := NewErrorResponse(requestID, message, errorDetail)

	assert.False(t, response.Success)
	assert.Equal(t, message, response.Message)
	assert.Equal(t, errorDetail, response.Error)
	assert.Equal(t, requestID, response.RequestID)
	assert.Nil(t, response.Data)
}

func TestWriteJSONResponse_Success(t *testing.T) {
	// Create test logger
	logger, err := zap.NewDevelopment()
	require.NoError(t, err)

	// Create test response
	response := NewSuccessResponse("test-123", "Success", map[string]string{"test": "data"})

	// Create test HTTP response writer
	w := httptest.NewRecorder()

	// Write JSON response
	WriteJSONResponse(w, logger, response, http.StatusOK)

	// Verify response
	assert.Equal(t, http.StatusOK, w.Code)
	assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

	// Parse and verify JSON
	var result APIResponse
	err = json.Unmarshal(w.Body.Bytes(), &result)
	require.NoError(t, err)

	assert.True(t, result.Success)
	assert.Equal(t, "Success", result.Message)
	assert.Equal(t, "test-123", result.RequestID)
}

func TestWriteJSONResponse_Error(t *testing.T) {
	// Create test logger
	logger, err := zap.NewDevelopment()
	require.NoError(t, err)

	// Create test error response
	response := NewErrorResponse("test-456", "Failed", "Error details")

	// Create test HTTP response writer
	w := httptest.NewRecorder()

	// Write JSON response
	WriteJSONResponse(w, logger, response, http.StatusBadRequest)

	// Verify response
	assert.Equal(t, http.StatusBadRequest, w.Code)
	assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

	// Parse and verify JSON
	var result APIResponse
	err = json.Unmarshal(w.Body.Bytes(), &result)
	require.NoError(t, err)

	assert.False(t, result.Success)
	assert.Equal(t, "Failed", result.Message)
	assert.Equal(t, "Error details", result.Error)
	assert.Equal(t, "test-456", result.RequestID)
}

func TestHealthData_JSONSerialization(t *testing.T) {
	healthData := &HealthData{
		Status:  "healthy",
		Uptime:  "2h30m45s",
		Version: "1.0.0",
	}

	// Marshal to JSON
	jsonData, err := json.Marshal(healthData)
	require.NoError(t, err)

	// Unmarshal back
	var result HealthData
	err = json.Unmarshal(jsonData, &result)
	require.NoError(t, err)

	assert.Equal(t, healthData.Status, result.Status)
	assert.Equal(t, healthData.Uptime, result.Uptime)
	assert.Equal(t, healthData.Version, result.Version)
}
