package models

import (
	"encoding/json"
	"net/http"

	"go.uber.org/zap"
)

// APIResponse represents the standardized HTTP response structure for consistent client interaction
type APIResponse struct {
	// Success indicates whether the operation was successful
	Success bool `json:"success"`
	// Message provides a human-readable operation result or error description
	Message string `json:"message"`
	// Data contains optional response payload (message content for GET /read, etc.)
	Data interface{} `json:"data,omitempty"`
	// Error provides detailed error information when Success=false
	Error string `json:"error,omitempty"`
	// RequestID provides correlation ID for debugging and logging
	RequestID string `json:"requestId"`
}

// HealthData represents the health check response data structure
type HealthData struct {
	// Status indicates the service health status
	Status string `json:"status"`
	// Uptime provides human-readable uptime format
	Uptime string `json:"uptime"`
	// Version provides service version identifier
	Version string `json:"version"`
}

// WriteJSONResponse writes an APIResponse as JSON to the HTTP response writer
func WriteJSONResponse(w http.ResponseWriter, logger *zap.Logger, response *APIResponse, statusCode int) {
	w.<PERSON>er().Set("Content-Type", "application/json")
	w.Write<PERSON>eader(statusCode)

	if err := json.NewEncoder(w).Encode(response); err != nil {
		logger.Error("Failed to encode JSON response",
			zap.Error(err),
			zap.String("request_id", response.RequestID),
		)
		// Fallback to plain text error
		w.WriteHeader(http.StatusInternalServerError)
		w.Write([]byte(`{"success": false, "message": "Internal server error"}`))
	}
}

// NewSuccessResponse creates a new successful APIResponse
func NewSuccessResponse(requestID, message string, data interface{}) *APIResponse {
	return &APIResponse{
		Success:   true,
		Message:   message,
		Data:      data,
		RequestID: requestID,
	}
}

// NewErrorResponse creates a new error APIResponse
func NewErrorResponse(requestID, message, errorDetail string) *APIResponse {
	return &APIResponse{
		Success:   false,
		Message:   message,
		Error:     errorDetail,
		RequestID: requestID,
	}
}
