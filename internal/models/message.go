package models

import (
	"encoding/json"
	"time"
)

// SQSMessage represents messages flowing through the system between HTTP API and AWS SQS FIFO queue
type SQSMessage struct {
	// ID is the AWS SQS MessageId for tracking and deduplication
	ID string `json:"id"`
	// Body is the original JSON payload from HTTP POST request
	Body json.RawMessage `json:"body"`
	// MessageGroupId is the FIFO queue grouping identifier for ordering
	MessageGroupId string `json:"messageGroupId"`
	// MessageDeduplicationId is the FIFO queue deduplication identifier
	MessageDeduplicationId string `json:"messageDeduplicationId"`
	// Timestamp is the message creation timestamp for debugging
	Timestamp time.Time `json:"timestamp"`
	// ReceiptHandle is the AWS SQS receipt handle for message deletion
	ReceiptHandle string `json:"receiptHandle,omitempty"`
}

// NewSQSMessage creates a new SQS message with the provided parameters
func NewSQSMessage(id, body, groupId, deduplicationId, receiptHandle string) *SQSMessage {
	return &SQSMessage{
		ID:                     id,
		Body:                   json.RawMessage(body),
		MessageGroupId:         groupId,
		MessageDeduplicationId: deduplicationId,
		Timestamp:              time.Now(),
		ReceiptHandle:          receiptHandle,
	}
}

// GetBodyAsString returns the message body as a string
func (m *SQSMessage) GetBodyAsString() string {
	return string(m.Body)
}

// SetBody sets the message body from a JSON-serializable object
func (m *SQSMessage) SetBody(data interface{}) error {
	bodyBytes, err := json.Marshal(data)
	if err != nil {
		return err
	}
	m.Body = json.RawMessage(bodyBytes)
	return nil
}
