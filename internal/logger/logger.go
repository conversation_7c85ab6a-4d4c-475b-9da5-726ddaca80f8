package logger

import (
	"strings"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// <PERSON><PERSON> wraps zap.Logger with additional functionality for the application
type Logger struct {
	*zap.Logger
}

// NewLogger creates a new structured logger with the specified log level
func NewLogger(logLevel string) (*Logger, error) {
	// Parse log level
	level, err := zapcore.ParseLevel(logLevel)
	if err != nil {
		return nil, err
	}

	// Configure logger for production (JSON output) or development
	var config zap.Config
	if logLevel == "debug" {
		config = zap.NewDevelopmentConfig()
	} else {
		config = zap.NewProductionConfig()
	}

	config.Level = zap.NewAtomicLevelAt(level)

	// Build logger
	zapLogger, err := config.Build()
	if err != nil {
		return nil, err
	}

	return &Logger{Logger: zapLogger}, nil
}

// WithRequestID adds a request correlation ID to the logger context
func (l *Logger) WithRequestID(requestID string) *Logger {
	return &Logger{
		Logger: l.Logger.With(zap.String("request_id", requestID)),
	}
}

// LogAPIRequest logs HTTP API request information
func (l *Logger) LogAPIRequest(method, path string, duration time.Duration) {
	l.Info("API request",
		zap.String("method", method),
		zap.String("path", path),
		zap.Duration("duration", duration),
	)
}

// LogServerStart logs server startup information
func (l *Logger) LogServerStart(port int) {
	l.Info("HTTP server starting",
		zap.Int("port", port),
	)
}

// LogServerShutdown logs server shutdown information
func (l *Logger) LogServerShutdown() {
	l.Info("HTTP server shutting down gracefully")
}

// LogSQSOperation logs AWS SQS operations with detailed context
func (l *Logger) LogSQSOperation(operation string, success bool, duration time.Duration, err error, metadata map[string]interface{}) {
	fields := []zap.Field{
		zap.String("operation", operation),
		zap.Bool("success", success),
		zap.Duration("duration", duration),
	}

	// Add metadata fields
	for key, value := range metadata {
		switch v := value.(type) {
		case string:
			fields = append(fields, zap.String(key, v))
		case int:
			fields = append(fields, zap.Int(key, v))
		case int64:
			fields = append(fields, zap.Int64(key, v))
		case bool:
			fields = append(fields, zap.Bool(key, v))
		default:
			fields = append(fields, zap.Any(key, v))
		}
	}

	if success {
		l.Logger.Info("SQS operation completed successfully", fields...)
	} else {
		if err != nil {
			fields = append(fields, zap.Error(err))
		}
		l.Logger.Error("SQS operation failed", fields...)
	}
}

// LogPerformanceMetrics logs performance metrics for monitoring
func (l *Logger) LogPerformanceMetrics(endpoint string, method string, statusCode int, duration time.Duration, requestSize, responseSize int64) {
	l.Logger.Info("Performance metrics",
		zap.String("endpoint", endpoint),
		zap.String("method", method),
		zap.Int("status_code", statusCode),
		zap.Duration("duration", duration),
		zap.Int64("request_size", requestSize),
		zap.Int64("response_size", responseSize),
	)
}

// LogSecurityEvent logs security-related events
func (l *Logger) LogSecurityEvent(event string, severity string, details map[string]interface{}) {
	fields := []zap.Field{
		zap.String("event", event),
		zap.String("severity", severity),
	}

	// Add detail fields (sanitized)
	for key, value := range details {
		// Skip sensitive fields
		if l.isSensitiveField(key) {
			continue
		}

		switch v := value.(type) {
		case string:
			fields = append(fields, zap.String(key, l.sanitizeValue(v)))
		case int:
			fields = append(fields, zap.Int(key, v))
		default:
			fields = append(fields, zap.Any(key, v))
		}
	}

	l.Logger.Warn("Security event", fields...)
}

// isSensitiveField checks if a field name contains sensitive information
func (l *Logger) isSensitiveField(fieldName string) bool {
	sensitiveFields := []string{
		"password", "secret", "key", "token", "credential",
		"authorization", "auth", "session", "cookie",
	}

	fieldLower := strings.ToLower(fieldName)
	for _, sensitive := range sensitiveFields {
		if strings.Contains(fieldLower, sensitive) {
			return true
		}
	}
	return false
}

// sanitizeValue removes or masks sensitive information from log values
func (l *Logger) sanitizeValue(value string) string {
	// Mask AWS credentials pattern
	if strings.Contains(strings.ToUpper(value), "AKIA") {
		return "[REDACTED-AWS-KEY]"
	}

	// Mask long tokens/secrets (more than 20 chars with mixed case/numbers)
	if len(value) > 20 && l.looksLikeSecret(value) {
		return "[REDACTED-TOKEN]"
	}

	return value
}

// looksLikeSecret heuristically determines if a string looks like a secret
func (l *Logger) looksLikeSecret(value string) bool {
	hasUpper := false
	hasLower := false
	hasDigit := false

	for _, char := range value {
		if char >= 'A' && char <= 'Z' {
			hasUpper = true
		} else if char >= 'a' && char <= 'z' {
			hasLower = true
		} else if char >= '0' && char <= '9' {
			hasDigit = true
		}
	}

	// Looks like a secret if it has mixed case and digits
	return hasUpper && hasLower && hasDigit
}

// Close flushes any buffered log entries
func (l *Logger) Close() error {
	return l.Logger.Sync()
}
