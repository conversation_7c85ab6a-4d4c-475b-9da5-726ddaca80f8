package logger

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewLogger_ValidLogLevels(t *testing.T) {
	validLevels := []string{"debug", "info", "warn", "error"}

	for _, level := range validLevels {
		t.Run(level, func(t *testing.T) {
			logger, err := NewLogger(level)
			require.NoError(t, err)
			assert.NotNil(t, logger)
			assert.NotNil(t, logger.Logger)

			// Clean up
			logger.Close()
		})
	}
}

func TestNewLogger_InvalidLogLevel(t *testing.T) {
	_, err := NewLogger("invalid")
	assert.Error(t, err)
}

func TestWithRequestID(t *testing.T) {
	logger, err := NewLogger("info")
	require.NoError(t, err)
	defer logger.Close()

	requestID := "test-request-123"
	loggerWithID := logger.WithRequestID(requestID)

	assert.NotNil(t, loggerWithID)
	assert.NotEqual(t, logger, loggerWithID) // Should be a new instance
}

func TestLogAPIRequest(t *testing.T) {
	logger, err := NewLogger("info")
	require.NoError(t, err)
	defer logger.Close()

	// This test verifies the method doesn't panic
	// In a real scenario, you might want to capture log output for verification
	logger.LogAPIRequest("GET", "/test", 100*time.Millisecond)
}

func TestLogServerStart(t *testing.T) {
	logger, err := NewLogger("info")
	require.NoError(t, err)
	defer logger.Close()

	// This test verifies the method doesn't panic
	logger.LogServerStart(8080)
}

func TestLogServerShutdown(t *testing.T) {
	logger, err := NewLogger("info")
	require.NoError(t, err)
	defer logger.Close()

	// This test verifies the method doesn't panic
	logger.LogServerShutdown()
}

func TestClose(t *testing.T) {
	logger, err := NewLogger("info")
	require.NoError(t, err)

	// Close may return an error in test environments due to stderr sync issues
	// This is expected and not a real failure
	logger.Close()
}

func TestLogSQSOperation_Success(t *testing.T) {
	log, err := NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	metadata := map[string]interface{}{
		"queue_url":     "https://sqs.us-east-1.amazonaws.com/123456789012/test-queue.fifo",
		"message_id":    "test-message-123",
		"message_count": 1,
	}

	// Should not panic
	log.LogSQSOperation("send-message", true, 100*time.Millisecond, nil, metadata)
}

func TestLogSQSOperation_Failure(t *testing.T) {
	log, err := NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	metadata := map[string]interface{}{
		"queue_url":  "https://sqs.us-east-1.amazonaws.com/123456789012/test-queue.fifo",
		"error_code": "AccessDenied",
	}

	testErr := fmt.Errorf("access denied to queue")

	// Should not panic
	log.LogSQSOperation("receive-message", false, 5*time.Second, testErr, metadata)
}

func TestLogPerformanceMetrics(t *testing.T) {
	log, err := NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	// Should not panic
	log.LogPerformanceMetrics("/queue", "POST", 200, 50*time.Millisecond, 1024, 256)
}

func TestLogSecurityEvent(t *testing.T) {
	log, err := NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	details := map[string]interface{}{
		"ip_address": "*************",
		"user_agent": "test-agent",
		"endpoint":   "/queue",
		"password":   "secret123",    // Should be filtered out
		"token":      "abc123def456", // Should be filtered out
	}

	// Should not panic
	log.LogSecurityEvent("suspicious_request", "medium", details)
}

func TestIsSensitiveField(t *testing.T) {
	log, err := NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	tests := []struct {
		field    string
		expected bool
	}{
		{"password", true},
		{"secret", true},
		{"api_key", true},
		{"authorization", true},
		{"session_token", true},
		{"username", false},
		{"email", false},
		{"endpoint", false},
		{"ip_address", false},
	}

	for _, test := range tests {
		result := log.isSensitiveField(test.field)
		assert.Equal(t, test.expected, result, "Field: %s", test.field)
	}
}

func TestSanitizeValue(t *testing.T) {
	log, err := NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	tests := []struct {
		input    string
		expected string
	}{
		{"AKIAIOSFODNN7EXAMPLE", "[REDACTED-AWS-KEY]"},
		{"akia123456789012345", "[REDACTED-AWS-KEY]"},
		{"normal-string", "normal-string"},
		{"short", "short"},
		{"ThisLooksLikeASecret123WithMixedCase", "[REDACTED-TOKEN]"},
		{"simple123", "simple123"}, // Too short to be considered secret
	}

	for _, test := range tests {
		result := log.sanitizeValue(test.input)
		assert.Equal(t, test.expected, result, "Input: %s", test.input)
	}
}

func TestLooksLikeSecret(t *testing.T) {
	log, err := NewLogger("info")
	require.NoError(t, err)
	defer log.Close()

	tests := []struct {
		input    string
		expected bool
	}{
		{"ThisHasUpperLowerAndDigits123", true},
		{"alllowercase123", false},
		{"ALLUPPERCASE123", false},
		{"NoDigitsHere", false},
		{"123456789", false},
		{"MixedCaseNoDigits", false},
		{"", false},
	}

	for _, test := range tests {
		result := log.looksLikeSecret(test.input)
		assert.Equal(t, test.expected, result, "Input: %s", test.input)
	}
}
