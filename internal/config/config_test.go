package config

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestLoadConfig_Defaults(t *testing.T) {
	// Clear environment variables
	os.Unsetenv("PORT")
	os.Unsetenv("LOG_LEVEL")
	os.Unsetenv("AWS_REGION")
	os.Unsetenv("SQS_QUEUE_URL")
	os.Unsetenv("MESSAGE_GROUP_ID")

	config, err := LoadConfig()
	require.NoError(t, err)

	assert.Equal(t, 8080, config.Port)
	assert.Equal(t, "info", config.LogLevel)
	assert.Equal(t, "us-east-1", config.AWSRegion)
	assert.Equal(t, "gomad-fifo-group", config.MessageGroupID)
	assert.Empty(t, config.SQSQueueURL) // No default for queue URL
}

func TestLoadConfig_FromEnvironment(t *testing.T) {
	// Set environment variables
	os.Setenv("PORT", "9090")
	os.Setenv("LOG_LEVEL", "debug")
	os.Setenv("AWS_REGION", "us-west-2")
	os.Setenv("SQS_QUEUE_URL", "https://sqs.us-west-2.amazonaws.com/123456789012/test-queue.fifo")
	os.Setenv("MESSAGE_GROUP_ID", "test-group")
	defer func() {
		os.Unsetenv("PORT")
		os.Unsetenv("LOG_LEVEL")
		os.Unsetenv("AWS_REGION")
		os.Unsetenv("SQS_QUEUE_URL")
		os.Unsetenv("MESSAGE_GROUP_ID")
	}()

	config, err := LoadConfig()
	require.NoError(t, err)

	assert.Equal(t, 9090, config.Port)
	assert.Equal(t, "debug", config.LogLevel)
	assert.Equal(t, "us-west-2", config.AWSRegion)
	assert.Equal(t, "https://sqs.us-west-2.amazonaws.com/123456789012/test-queue.fifo", config.SQSQueueURL)
	assert.Equal(t, "test-group", config.MessageGroupID)
}

func TestLoadConfig_InvalidPort(t *testing.T) {
	tests := []struct {
		name      string
		portValue string
	}{
		{"non-numeric", "invalid"},
		{"negative", "-1"},
		{"zero", "0"},
		{"too-large", "70000"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			os.Setenv("PORT", tt.portValue)
			defer os.Unsetenv("PORT")

			_, err := LoadConfig()
			assert.Error(t, err)
		})
	}
}

func TestValidate_ValidConfig(t *testing.T) {
	config := &Configuration{
		Port:           8080,
		LogLevel:       "info",
		AWSRegion:      "us-east-1",
		MessageGroupID: "test-group",
	}

	err := config.Validate()
	assert.NoError(t, err)
}

func TestValidate_InvalidPort(t *testing.T) {
	tests := []struct {
		name string
		port int
	}{
		{"negative", -1},
		{"zero", 0},
		{"too-large", 70000},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config := &Configuration{
				Port:           tt.port,
				LogLevel:       "info",
				AWSRegion:      "us-east-1",
				MessageGroupID: "test-group",
			}

			err := config.Validate()
			assert.Error(t, err)
		})
	}
}

func TestValidate_InvalidLogLevel(t *testing.T) {
	config := &Configuration{
		Port:           8080,
		LogLevel:       "invalid",
		AWSRegion:      "us-east-1",
		MessageGroupID: "test-group",
	}

	err := config.Validate()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid log level")
}

func TestValidate_ValidLogLevels(t *testing.T) {
	validLevels := []string{"debug", "info", "warn", "error"}

	for _, level := range validLevels {
		t.Run(level, func(t *testing.T) {
			config := &Configuration{
				Port:           8080,
				LogLevel:       level,
				AWSRegion:      "us-east-1",
				MessageGroupID: "test-group",
			}

			err := config.Validate()
			assert.NoError(t, err)
		})
	}
}

func TestValidate_MissingAWSRegion(t *testing.T) {
	config := &Configuration{
		Port:           8080,
		LogLevel:       "info",
		AWSRegion:      "", // Missing region
		MessageGroupID: "test-group",
	}

	err := config.Validate()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "AWS region is required")
}

func TestValidate_MissingMessageGroupID(t *testing.T) {
	config := &Configuration{
		Port:           8080,
		LogLevel:       "info",
		AWSRegion:      "us-east-1",
		MessageGroupID: "", // Missing message group ID
	}

	err := config.Validate()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "message group ID is required")
}
