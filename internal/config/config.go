package config

import (
	"context"
	"fmt"
	"os"
	"strconv"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
)

// Configuration holds all application configuration loaded from environment variables
type Configuration struct {
	// Port is the HTTP server port (default 8080)
	Port int
	// LogLevel is the Zap logging level (debug, info, warn, error)
	LogLevel string
	// AWSRegion is the AWS region for SQS operations
	AWSRegion string
	// SQSQueueURL is the complete FIFO queue URL for message operations
	SQSQueueURL string
	// MessageGroupID is the FIFO queue message group identifier strategy
	MessageGroupID string
}

// LoadConfig loads and validates configuration from environment variables
func LoadConfig() (*Configuration, error) {
	config := &Configuration{
		Port:           8080,               // default port
		LogLevel:       "info",             // default log level
		AWSRegion:      "us-east-1",        // default AWS region
		MessageGroupID: "gomad-fifo-group", // default message group ID
	}

	// Load port from environment
	if portStr := os.Getenv("PORT"); portStr != "" {
		port, err := strconv.Atoi(portStr)
		if err != nil {
			return nil, fmt.Errorf("invalid PORT value: %w", err)
		}
		if port <= 0 || port > 65535 {
			return nil, fmt.Errorf("PORT must be between 1 and 65535, got %d", port)
		}
		config.Port = port
	}

	// Load log level from environment
	if logLevel := os.Getenv("LOG_LEVEL"); logLevel != "" {
		config.LogLevel = logLevel
	}

	// Load AWS region from environment
	if awsRegion := os.Getenv("AWS_REGION"); awsRegion != "" {
		config.AWSRegion = awsRegion
	}

	// Load SQS queue URL from environment (required for production)
	if sqsQueueURL := os.Getenv("SQS_QUEUE_URL"); sqsQueueURL != "" {
		config.SQSQueueURL = sqsQueueURL
	}

	// Load message group ID from environment
	if messageGroupID := os.Getenv("MESSAGE_GROUP_ID"); messageGroupID != "" {
		config.MessageGroupID = messageGroupID
	}

	// Validate configuration
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", err)
	}

	return config, nil
}

// Validate checks that all required configuration fields are valid
func (c *Configuration) Validate() error {
	// Validate port
	if c.Port <= 0 || c.Port > 65535 {
		return fmt.Errorf("port must be between 1 and 65535, got %d", c.Port)
	}

	// Validate log level
	validLogLevels := map[string]bool{
		"debug": true,
		"info":  true,
		"warn":  true,
		"error": true,
	}
	if !validLogLevels[c.LogLevel] {
		return fmt.Errorf("invalid log level: %s, must be one of: debug, info, warn, error", c.LogLevel)
	}

	// Validate AWS region
	if c.AWSRegion == "" {
		return fmt.Errorf("AWS region is required")
	}

	// Validate message group ID for FIFO queue
	if c.MessageGroupID == "" {
		return fmt.Errorf("message group ID is required for FIFO queue operations")
	}

	// Note: SQS queue URL is optional for development/testing but required for production
	// Production deployment should set SQS_QUEUE_URL environment variable

	return nil
}

// GetAWSConfig creates and returns AWS configuration for SQS client
func (c *Configuration) GetAWSConfig(ctx context.Context) (aws.Config, error) {
	// Create context with timeout for AWS config loading
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	// Load AWS configuration
	cfg, err := config.LoadDefaultConfig(ctx,
		config.WithRegion(c.AWSRegion),
	)
	if err != nil {
		return aws.Config{}, fmt.Errorf("failed to load AWS config: %w", err)
	}

	return cfg, nil
}

// NewSQSClient creates a new SQS client with the configuration
func (c *Configuration) NewSQSClient(ctx context.Context) (*sqs.Client, error) {
	awsConfig, err := c.GetAWSConfig(ctx)
	if err != nil {
		return nil, err
	}

	client := sqs.NewFromConfig(awsConfig)
	return client, nil
}
