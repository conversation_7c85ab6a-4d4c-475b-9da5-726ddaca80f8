# Go SQS API Makefile

# Variables
APP_NAME := gomad
DOCKER_TAG := $(APP_NAME):latest
DOCKER_COMPOSE_FILE := deployments/docker-compose.yml
DOCKER_COMPOSE_PROD_FILE := deployments/docker-compose.prod.yml

# Go variables
GO_VERSION := 1.21.12
BINARY_NAME := server
BUILD_DIR := bin

# Default target
.DEFAULT_GOAL := help

# Help target
.PHONY: help
help: ## Show this help message
	@echo "Go SQS API - Available targets:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[36m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Development targets
.PHONY: build
build: ## Build the Go application
	@echo "Building $(BINARY_NAME)..."
	@mkdir -p $(BUILD_DIR)
	@go build -o $(BUILD_DIR)/$(BINARY_NAME) ./cmd/server
	@echo "Build complete: $(BUILD_DIR)/$(BINARY_NAME)"

.PHONY: run
run: build ## Build and run the application locally
	@echo "Starting $(APP_NAME)..."
	@./$(BUILD_DIR)/$(BINARY_NAME)

.PHONY: test
test: ## Run all tests
	@echo "Running tests..."
	@go test ./...

.PHONY: test-verbose
test-verbose: ## Run tests with verbose output
	@echo "Running tests with verbose output..."
	@go test -v ./...

.PHONY: test-integration
test-integration: ## Run integration tests
	@echo "Running integration tests..."
	@./scripts/test.sh integration

.PHONY: test-integration-localstack
test-integration-localstack: ## Run integration tests with LocalStack
	@echo "Running integration tests with LocalStack..."
	@./scripts/test.sh integration --localstack

.PHONY: test-security
test-security: ## Run security scans
	@echo "Running security scans..."
	@./scripts/test.sh security

.PHONY: test-performance
test-performance: ## Run performance tests
	@echo "Running performance tests..."
	@./scripts/test.sh performance

.PHONY: test-all
test-all: ## Run all tests with coverage
	@echo "Running all tests..."
	@./scripts/test.sh all --coverage

.PHONY: clean
clean: ## Clean build artifacts
	@echo "Cleaning build artifacts..."
	@rm -rf $(BUILD_DIR)
	@go clean

# Docker targets
.PHONY: docker-build
docker-build: ## Build Docker image
	@echo "Building Docker image..."
	@./scripts/docker-build.sh $(DOCKER_TAG)

.PHONY: docker-run
docker-run: ## Run Docker container
	@echo "Running Docker container..."
	@docker run -p 8080:8080 --rm $(DOCKER_TAG)

.PHONY: docker-shell
docker-shell: ## Open shell in Docker container
	@echo "Opening shell in Docker container..."
	@docker run -it --rm $(DOCKER_TAG) /bin/sh

# Docker Compose targets
.PHONY: up
up: ## Start services with docker-compose
	@echo "Starting services with docker-compose..."
	@cd deployments && docker-compose up

.PHONY: up-build
up-build: ## Build and start services with docker-compose
	@echo "Building and starting services with docker-compose..."
	@cd deployments && docker-compose up --build

.PHONY: up-detached
up-detached: ## Start services in background with docker-compose
	@echo "Starting services in background..."
	@cd deployments && docker-compose up -d

.PHONY: down
down: ## Stop and remove services
	@echo "Stopping services..."
	@cd deployments && docker-compose down

.PHONY: logs
logs: ## Show logs from all services
	@echo "Showing logs..."
	@cd deployments && docker-compose logs -f

.PHONY: logs-api
logs-api: ## Show logs from API service only
	@echo "Showing API logs..."
	@cd deployments && docker-compose logs -f gomad-api

# Production targets
.PHONY: prod-up
prod-up: ## Start production services
	@echo "Starting production services..."
	@cd deployments && docker-compose -f $(DOCKER_COMPOSE_PROD_FILE) up

.PHONY: prod-up-detached
prod-up-detached: ## Start production services in background
	@echo "Starting production services in background..."
	@cd deployments && docker-compose -f $(DOCKER_COMPOSE_PROD_FILE) up -d

.PHONY: prod-down
prod-down: ## Stop production services
	@echo "Stopping production services..."
	@cd deployments && docker-compose -f $(DOCKER_COMPOSE_PROD_FILE) down

# Utility targets
.PHONY: fmt
fmt: ## Format Go code
	@echo "Formatting Go code..."
	@go fmt ./...

.PHONY: lint
lint: ## Run golangci-lint
	@echo "Running linter..."
	@golangci-lint run

.PHONY: deps
deps: ## Download and tidy dependencies
	@echo "Downloading dependencies..."
	@go mod download
	@go mod tidy

.PHONY: deps-update
deps-update: ## Update dependencies
	@echo "Updating dependencies..."
	@go get -u ./...
	@go mod tidy

# Health check targets
.PHONY: health
health: ## Check application health
	@echo "Checking application health..."
	@curl -f http://localhost:8080/health || echo "Health check failed"

.PHONY: docker-health
docker-health: ## Check Docker container health
	@echo "Checking Docker container health..."
	@docker ps --filter "name=gomad" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# All-in-one targets
.PHONY: dev
dev: clean build test ## Clean, build, and test for development

.PHONY: ci
ci: clean build test-verbose ## Run CI pipeline (clean, build, test)

.PHONY: docker-dev
docker-dev: docker-build up-detached ## Build Docker image and start development environment
