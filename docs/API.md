# GoMAD API Documentation

## Overview

GoMAD provides a simple REST API for message queuing operations. All endpoints return JSON responses with consistent structure and proper HTTP status codes.

## Base URL

```
http://localhost:8080
```

## Authentication

GoMAD uses AWS IAM for authentication to SQS services. No API keys are required for the HTTP endpoints themselves.

## Request/Response Format

### Request Headers

All POST requests must include:
```
Content-Type: application/json
```

### Response Structure

All responses follow this consistent structure:

**Success Response:**
```json
{
  "success": true,
  "message": "Operation description",
  "data": {
    // Response data object
  },
  "request_id": "req-xxxxxxxx"
}
```

**Error Response:**
```json
{
  "success": false,
  "message": "Error description",
  "request_id": "req-xxxxxxxx"
}
```

### Request Correlation

Every request receives a unique correlation ID in the format `req-xxxxxxxx` where `x` is a random alphanumeric character. This ID is:
- Returned in all responses
- Logged with all related log entries
- Used for request tracing and debugging

## Endpoints

### Health Check

Check the health and status of the service.

```http
GET /health
```

**Response (200 OK):**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "uptime": "2h 15m 30s",
  "version": "1.0.0",
  "request_id": "req-a1b2c3d4"
}
```

**Response Fields:**
- `status`: Always "healthy" when service is operational
- `timestamp`: Current server time in ISO 8601 format
- `uptime`: Human-readable uptime duration
- `version`: Application version
- `request_id`: Unique request correlation ID

### Submit Message

Submit a JSON message to the queue for processing.

```http
POST /queue
Content-Type: application/json
```

**Request Body:**

Any valid JSON object. Examples:

**Simple Message:**
```json
{
  "orderId": "order-12345",
  "amount": 99.99,
  "status": "pending"
}
```

**Complex Nested Message:**
```json
{
  "orderId": "order-12345",
  "customerId": "customer-67890",
  "amount": 99.99,
  "items": [
    {
      "productId": "prod-001",
      "name": "Widget A",
      "quantity": 2,
      "price": 49.99
    },
    {
      "productId": "prod-002", 
      "name": "Widget B",
      "quantity": 1,
      "price": 0.01
    }
  ],
  "shipping": {
    "address": {
      "street": "123 Main St",
      "city": "Anytown",
      "state": "CA",
      "zip": "12345"
    },
    "method": "standard",
    "cost": 5.99
  },
  "metadata": {
    "source": "web",
    "campaign": "summer-sale",
    "userAgent": "Mozilla/5.0...",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Message queued successfully",
  "data": {
    "message_id": "msg-a1b2c3d4-e5f6-7890-abcd-ef1234567890",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "request_id": "req-a1b2c3d4"
}
```

**Response Fields:**
- `message_id`: Unique SQS message identifier
- `timestamp`: When the message was queued

**Error Responses:**

**400 Bad Request - Invalid JSON:**
```json
{
  "success": false,
  "message": "invalid JSON syntax: unexpected character '}' at position 15",
  "request_id": "req-a1b2c3d4"
}
```

**400 Bad Request - Empty Body:**
```json
{
  "success": false,
  "message": "request body cannot be empty",
  "request_id": "req-a1b2c3d4"
}
```

**400 Bad Request - Null Payload:**
```json
{
  "success": false,
  "message": "JSON payload cannot be null",
  "request_id": "req-a1b2c3d4"
}
```

**400 Bad Request - Empty Object:**
```json
{
  "success": false,
  "message": "JSON payload cannot be empty object",
  "request_id": "req-a1b2c3d4"
}
```

**400 Bad Request - Invalid Content Type:**
```json
{
  "success": false,
  "message": "Content-Type must be application/json",
  "request_id": "req-a1b2c3d4"
}
```

**405 Method Not Allowed:**
```json
{
  "success": false,
  "message": "Only POST method is supported",
  "request_id": "req-a1b2c3d4"
}
```

**500 Internal Server Error:**
```json
{
  "success": false,
  "message": "Queue service unavailable",
  "request_id": "req-a1b2c3d4"
}
```

### Retrieve Message

Retrieve the next available message from the queue. Messages are returned in FIFO order and automatically deleted after retrieval.

```http
GET /read
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Message retrieved successfully",
  "data": {
    "message_id": "msg-a1b2c3d4-e5f6-7890-abcd-ef1234567890",
    "body": {
      "orderId": "order-12345",
      "customerId": "customer-67890",
      "amount": 99.99,
      "items": [
        {
          "productId": "prod-001",
          "quantity": 2,
          "price": 49.99
        }
      ]
    },
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "request_id": "req-e5f6g7h8"
}
```

**Response Fields:**
- `message_id`: Unique SQS message identifier
- `body`: The original JSON message that was submitted
- `timestamp`: When the message was originally queued

**Response (204 No Content):**

When no messages are available in the queue, the endpoint returns HTTP 204 with an empty body.

**Error Responses:**

**405 Method Not Allowed:**
```json
{
  "success": false,
  "message": "Only GET method is supported",
  "request_id": "req-a1b2c3d4"
}
```

**500 Internal Server Error:**
```json
{
  "success": false,
  "message": "Queue service unavailable",
  "request_id": "req-a1b2c3d4"
}
```

## HTTP Status Codes

| Code | Description | When Used |
|------|-------------|-----------|
| 200 | OK | Successful operation |
| 204 | No Content | No messages available in queue |
| 400 | Bad Request | Invalid request format, JSON, or content |
| 405 | Method Not Allowed | Wrong HTTP method for endpoint |
| 500 | Internal Server Error | SQS service errors or internal failures |

## Rate Limiting

Currently, no rate limiting is implemented. The service can handle 10,000+ requests per second under normal conditions.

## CORS Support

The API includes CORS headers for cross-origin requests:

```
Access-Control-Allow-Origin: *
Access-Control-Allow-Methods: GET, POST, OPTIONS
Access-Control-Allow-Headers: Content-Type, Authorization
Access-Control-Max-Age: 86400
```

## Security Headers

All responses include security headers:

```
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Referrer-Policy: strict-origin-when-cross-origin
```

## Message Ordering

Messages are processed in FIFO (First-In-First-Out) order using AWS SQS FIFO queues. This guarantees:

1. **Ordering**: Messages are delivered in the exact order they were sent
2. **Deduplication**: Duplicate messages are automatically detected and removed
3. **Exactly-Once Processing**: Each message is delivered exactly once

## Message Size Limits

- **Maximum message size**: 256 KB (AWS SQS limit)
- **Recommended size**: Under 64 KB for optimal performance
- **JSON nesting**: No artificial limits on nesting depth

## Error Handling

All errors are logged with structured logging including:
- Request correlation ID
- Error details and stack traces
- Request timing and metadata
- SQS operation details

Clients should always check the `success` field in responses and handle errors appropriately.

## Examples

### cURL Examples

**Submit a message:**
```bash
curl -X POST http://localhost:8080/queue \
  -H "Content-Type: application/json" \
  -d '{"orderId": "order-123", "amount": 99.99}'
```

**Retrieve a message:**
```bash
curl http://localhost:8080/read
```

**Health check:**
```bash
curl http://localhost:8080/health
```

### JavaScript Examples

**Submit a message:**
```javascript
const response = await fetch('http://localhost:8080/queue', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    orderId: 'order-123',
    amount: 99.99,
    items: [
      { productId: 'prod-001', quantity: 2 }
    ]
  })
});

const result = await response.json();
console.log('Message ID:', result.data.message_id);
```

**Retrieve a message:**
```javascript
const response = await fetch('http://localhost:8080/read');

if (response.status === 204) {
  console.log('No messages available');
} else {
  const result = await response.json();
  console.log('Message:', result.data.body);
}
```

### Python Examples

**Submit a message:**
```python
import requests
import json

response = requests.post('http://localhost:8080/queue', 
  headers={'Content-Type': 'application/json'},
  json={
    'orderId': 'order-123',
    'amount': 99.99,
    'items': [
      {'productId': 'prod-001', 'quantity': 2}
    ]
  }
)

result = response.json()
print(f"Message ID: {result['data']['message_id']}")
```

**Retrieve a message:**
```python
response = requests.get('http://localhost:8080/read')

if response.status_code == 204:
  print('No messages available')
else:
  result = response.json()
  print(f"Message: {result['data']['body']}")
```
