# Core Workflows

```mermaid
sequenceDiagram
    participant C as Client App
    participant H as HTTP Server
    participant S as SQS Service
    participant Q as AWS SQS FIFO
    participant L as Logger

    Note over C,L: Message Submission Workflow
    C->>H: POST /queue {json payload}
    H->>L: Log incoming request
    H->>H: Validate JSON payload
    H->>S: SendMessage(json)
    S->>S: Generate MessageGroupId & DeduplicationId
    S->>Q: SendMessage with FIFO metadata
    Q-->>S: MessageId response
    S-->>H: Success with MessageId
    H->>L: Log successful submission
    H-->>C: HTTP 200 {success: true, messageId}

    Note over C,L: Message Retrieval Workflow  
    C->>H: GET /read
    H->>L: Log retrieval request
    H->>S: ReceiveMessage()
    S->>Q: ReceiveMessage(MaxMessages=1)
    
    alt Message Available
        Q-->>S: Message with ReceiptHandle
        S->>Q: DeleteMessage(ReceiptHandle)
        Q-->>S: Delete confirmation
        S-->>H: Message data
        H->>L: Log successful retrieval
        H-->>C: HTTP 200 {message data}
    else Queue Empty
        Q-->>S: No messages available
        S-->>H: Empty result
        H->>L: Log empty queue
        H-->>C: HTTP 204 No Content
    end

    Note over C,L: Error Handling Workflow
    C->>H: POST /queue {invalid json}
    H->>H: JSON validation fails
    H->>L: Log validation error
    H-->>C: HTTP 400 {error details}
    
    C->>H: GET /read
    H->>S: ReceiveMessage()
    S->>Q: ReceiveMessage fails (AWS unavailable)
    Q-->>S: Service error
    S-->>H: Error with retry info
    H->>L: Log AWS service error
    H-->>C: HTTP 500 {service unavailable}
```
