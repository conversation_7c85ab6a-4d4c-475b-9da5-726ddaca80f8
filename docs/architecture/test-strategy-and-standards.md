# Test Strategy and Standards

## Testing Philosophy
- **Approach:** Test-driven development with comprehensive coverage for critical paths (HTTP endpoints, SQS operations)
- **Coverage Goals:** 80% unit test coverage, 100% coverage for public APIs and error paths
- **Test Pyramid:** 70% unit tests, 25% integration tests, 5% end-to-end tests

## Test Types and Organization

### Unit Tests
- **Framework:** testing (stdlib) + testify v1.9.0
- **File Convention:** `*_test.go` co-located with source files
- **Location:** Alongside source in `internal/` packages
- **Mocking Library:** `testify/mock` for interface mocking
- **Coverage Requirement:** 80% minimum for all packages

**AI Agent Requirements:**
- Generate tests for all public methods and HTTP handlers
- Cover edge cases and error conditions (invalid JSON, AWS failures, empty queues)
- Follow AAA pattern (Arrange, Act, Assert)
- Mock all external dependencies (AWS SDK, configuration)

### Integration Tests
- **Scope:** Full API testing with real AWS SQS operations using test queue
- **Location:** `test/integration/` directory
- **Test Infrastructure:**
  - **AWS SQS:** Real FIFO test queue with dedicated AWS credentials
  - **HTTP Server:** Full server startup with test configuration
  - **Docker Compose:** LocalStack SQS emulator for offline testing

### End-to-End Tests
- **Framework:** Go testing with HTTP client
- **Scope:** Complete user workflows (POST message → GET message verification)
- **Environment:** Containerized service with real SQS test queue
- **Test Data:** JSON fixtures in `test/integration/testdata/`

## Test Data Management
- **Strategy:** JSON fixture files with valid/invalid message examples
- **Fixtures:** `test/integration/testdata/` with categorized test cases
- **Factories:** Builder pattern for test message creation with randomized data
- **Cleanup:** Automatic SQS queue purging after each integration test suite

## Continuous Testing
- **CI Integration:** GitHub Actions runs unit tests on PR, integration tests on merge to main
- **Performance Tests:** Benchmark tests for critical paths (JSON parsing, SQS operations) 
- **Security Tests:** Go security scanner (`gosec`) in CI pipeline for vulnerability detection
