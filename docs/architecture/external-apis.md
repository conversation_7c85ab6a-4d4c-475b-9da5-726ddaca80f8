# External APIs

## AWS SQS API

- **Purpose:** FIFO queue operations for message ordering and reliable delivery
- **Documentation:** https://docs.aws.amazon.com/sqs/latest/APIReference/
- **Base URL(s):** https://sqs.{region}.amazonaws.com/{account-id}/{queue-name}.fifo
- **Authentication:** AWS Signature Version 4 with IAM credentials (Access Key/Secret or IAM Role)
- **Rate Limits:** 300 API calls per second for FIFO queues, 120,000 inflight messages maximum

**Key Endpoints Used:**
- `POST {queue-url}` - SendMessage action for queue submission with MessageGroupId/MessageDeduplicationId
- `POST {queue-url}` - ReceiveMessage action for message retrieval with MaxNumberOfMessages=1
- `POST {queue-url}` - DeleteMessage action for message cleanup after successful processing

**Integration Notes:** AWS SDK for Go v2 handles authentication, retries, and error handling. FIFO queues require MessageGroupId for ordering and MessageDeduplicationId for deduplication. Service uses single message group for strict FIFO ordering across all messages.
