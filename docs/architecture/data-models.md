# Data Models

## SQSMessage

**Purpose:** Represents messages flowing through the system between HTTP API and AWS SQS FIFO queue

**Key Attributes:**
- **ID**: string - AWS SQS MessageId for tracking and deduplication  
- **Body**: json.RawMessage - Original JSON payload from HTTP POST request
- **MessageGroupId**: string - FIFO queue grouping identifier for ordering
- **MessageDeduplicationId**: string - FIFO queue deduplication identifier
- **Timestamp**: time.Time - Message creation timestamp for debugging
- **ReceiptHandle**: string - AWS SQS receipt handle for message deletion

**Relationships:**
- Maps 1:1 with AWS SQS FIFO messages
- Contains original HTTP request payload as JSON

## APIResponse

**Purpose:** Standardized HTTP response structure for consistent client interaction

**Key Attributes:**
- **Success**: bool - Operation success indicator
- **Message**: string - Human-readable operation result or error description
- **Data**: interface{} - Optional response payload (message content for GET /read)
- **Error**: string - Detailed error information when Success=false
- **RequestID**: string - Correlation ID for debugging and logging

**Relationships:**
- Used by all HTTP endpoints for consistent response format
- Links to logging via RequestID for debugging

## Configuration

**Purpose:** Application configuration structure loaded from environment variables

**Key Attributes:**
- **Port**: int - HTTP server port (default 8080)
- **AWSRegion**: string - AWS region for SQS operations  
- **SQSQueueURL**: string - Complete FIFO queue URL for message operations
- **LogLevel**: string - Zap logging level (debug, info, warn, error)
- **MessageGroupID**: string - FIFO queue message group identifier strategy

**Relationships:**
- Drives AWS SDK client initialization
- Controls application behavior and external service connections
