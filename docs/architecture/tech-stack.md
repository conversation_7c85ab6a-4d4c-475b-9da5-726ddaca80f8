# Tech Stack

## Cloud Infrastructure
- **Provider:** Amazon Web Services (AWS)
- **Key Services:** SQS FIFO, IAM for credentials, CloudWatch for monitoring
- **Deployment Regions:** Configurable via environment (recommend us-east-1 for cost/latency)

## Technology Stack Table

| Category | Technology | Version | Purpose | Rationale |
|----------|------------|---------|---------|-----------|
| **Language** | Go | 1.21.12 | Primary development language | LTS version, excellent concurrency, AWS SDK support, PRD requirement |
| **Runtime** | Go Runtime | 1.21.12 | Application runtime | Matches language version, stable performance |
| **HTTP Framework** | net/http (stdlib) | 1.21.12 | HTTP server and routing | Zero external dependencies, high performance, PRD emphasizes minimal footprint |
| **AWS SDK** | AWS SDK for Go v2 | 1.30.3 | SQS FIFO operations | Official AWS SDK, latest stable, excellent FIFO support |
| **Logging** | go.uber.org/zap | v1.27.0 | Structured logging | Zero-allocation performance, critical for 100ms SLA and 1000+ req/s targets |
| **Testing** | testing + testify | v1.9.0 | Unit and integration tests | Standard library + common assertions, PRD requires comprehensive testing |
| **Container Base** | Alpine Linux | 3.19 | Container base image | Minimal size, security-focused, PRD requirement |
| **Container Runtime** | Docker | 24.x+ | Containerization | Industry standard, PRD requirement for consistent deployment |
| **Configuration** | Environment Variables | - | Application configuration | 12-factor app compliance, container-friendly |
