# Components

## HTTP Server

**Responsibility:** HTTP request handling, routing, middleware, and response formatting

**Key Interfaces:**
- `POST /queue` - Accept JSON payloads for queue submission
- `GET /read` - Retrieve messages from queue in FIFO order  
- `GET /health` - Health check for container orchestration

**Dependencies:** Configuration, SQS Service, Logger

**Technology Stack:** Go net/http stdlib, custom middleware for logging/CORS, Zap logger integration

## SQS Service

**Responsibility:** AWS SQS FIFO queue operations, message formatting, and error handling

**Key Interfaces:**
- `SendMessage(body json.RawMessage) (*SQSMessage, error)` - Send to FIFO queue
- `ReceiveMessage() (*SQSMessage, error)` - Receive from FIFO queue with auto-delete
- `GenerateMessageIDs() (groupId, deduplicationId string)` - FIFO ID generation

**Dependencies:** AWS SDK v2 SQS Client, Configuration, Logger

**Technology Stack:** AWS SDK for Go v2, SQS FIFO-specific operations, circuit breaker pattern

## Configuration Manager

**Responsibility:** Environment variable loading, validation, and application configuration

**Key Interfaces:**
- `LoadConfig() (*Configuration, error)` - Load and validate environment configuration
- `GetAWSConfig() aws.Config` - AWS SDK configuration with credentials
- `Validate() error` - Configuration validation and required field checking

**Dependencies:** Environment Variables, AWS SDK Config

**Technology Stack:** Go stdlib env parsing, AWS SDK config loading, validation logic

## Logger Service

**Responsibility:** Structured logging, request correlation, and observability

**Key Interfaces:**
- `WithRequestID(id string) Logger` - Add request correlation
- `LogAPIRequest(method, path string, duration time.Duration)` - HTTP request logging
- `LogSQSOperation(operation string, success bool, error)` - SQS operation logging

**Dependencies:** Zap logger, Configuration

**Technology Stack:** Zap structured logger, JSON output format, configurable log levels

## Component Diagrams

```mermaid
graph TB
    subgraph "Go SQS API Service"
        A[HTTP Server] --> B[SQS Service]
        A --> D[Logger Service]
        B --> D
        C[Configuration Manager] --> A
        C --> B
        C --> D
        
        A --> E["/queue Handler"]
        A --> F["/read Handler"] 
        A --> G["/health Handler"]
        
        B --> H[AWS SQS Client]
        C --> I[Environment Variables]
        D --> J[Structured JSON Logs]
    end
    
    K[Client Applications] --> A
    H --> L[AWS SQS FIFO Queue]
    J --> M[Container Log Aggregation]
    
    style A fill:#e3f2fd
    style B fill:#fff3e0  
    style C fill:#f1f8e9
    style D fill:#fce4ec
```
