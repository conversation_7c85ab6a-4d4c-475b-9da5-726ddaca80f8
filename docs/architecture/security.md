# Security

## Input Validation
- **Validation Library:** Go standard library `json` package with custom validation functions
- **Validation Location:** HTTP handler level before business logic processing
- **Required Rules:**
  - All HTTP request bodies MUST be valid JSON (reject malformed payloads)
  - JSON payload size limited to 256KB maximum
  - Whitelist approach: reject requests with unexpected headers or methods

## Authentication & Authorization
- **Auth Method:** None required - stateless API service for internal/trusted network deployment
- **Session Management:** Not applicable - each HTTP request is independent
- **Required Patterns:**
  - Service deployed behind network security groups/firewall rules
  - Container network isolation in production deployments

## Secrets Management
- **Development:** Environment variables with `.env` file (excluded from git)
- **Production:** Container environment variables injected by orchestration platform
- **Code Requirements:**
  - NEVER hardcode AWS credentials or sensitive configuration
  - Access via `os.Getenv()` only with validation on startup
  - No secrets in logs, error messages, or debug output

## API Security
- **Rate Limiting:** Rely on container platform/load balancer rate limiting
- **CORS Policy:** Restrictive CORS headers - allow only necessary origins
- **Security Headers:** `X-Content-Type-Options: nosniff`, `X-Frame-Options: DENY`
- **HTTPS Enforcement:** TLS termination at load balancer/reverse proxy level

## Data Protection
- **Encryption at Rest:** AWS SQS server-side encryption (SSE-SQS) enabled on FIFO queue
- **Encryption in Transit:** HTTPS for API endpoints, AWS SDK uses TLS for SQS communication
- **PII Handling:** No PII processing - service is payload-agnostic message bridge
- **Logging Restrictions:** Never log message payloads, AWS credentials, or request bodies

## Dependency Security
- **Scanning Tool:** `go mod audit` and GitHub Dependabot for dependency vulnerabilities
- **Update Policy:** Monthly dependency updates with automated security patch application
- **Approval Process:** New dependencies require architecture review for minimal footprint principle

## Security Testing
- **SAST Tool:** `gosec` static analysis in CI pipeline for Go-specific vulnerabilities
- **DAST Tool:** Not applicable - internal API service without public endpoints
- **Penetration Testing:** Not required - focused internal service with limited attack surface
