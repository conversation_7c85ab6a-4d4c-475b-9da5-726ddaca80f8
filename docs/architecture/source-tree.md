# Source Tree

```plaintext
gomad/
├── cmd/
│   └── server/
│       └── main.go                    # Application entry point with server startup
├── internal/
│   ├── config/
│   │   ├── config.go                  # Configuration struct and environment loading
│   │   └── config_test.go             # Configuration validation tests
│   ├── handlers/
│   │   ├── health.go                  # GET /health endpoint handler
│   │   ├── queue.go                   # POST /queue endpoint handler
│   │   ├── read.go                    # GET /read endpoint handler
│   │   ├── middleware.go              # Request logging and CORS middleware
│   │   └── handlers_test.go           # HTTP handler unit tests
│   ├── services/
│   │   ├── sqs.go                     # SQS service implementation
│   │   ├── sqs_interface.go           # SQS service interface for testing
│   │   └── sqs_test.go                # SQS service unit tests
│   ├── models/
│   │   ├── api.go                     # APIResponse and request/response structs
│   │   ├── message.go                 # SQSMessage struct and methods
│   │   └── models_test.go             # Model validation tests
│   └── logger/
│       ├── logger.go                  # Zap logger setup and structured logging
│       └── logger_test.go             # Logger configuration tests
├── test/
│   ├── integration/
│   │   ├── api_test.go                # Full API integration tests with test SQS
│   │   ├── docker-compose.test.yml    # Test environment with LocalStack SQS
│   │   └── testdata/
│   │       ├── valid_messages.json    # Test message payloads
│   │       └── invalid_messages.json  # Invalid payload test cases
│   └── mocks/
│       └── sqs_mock.go                # Mock SQS service for unit tests
├── scripts/
│   ├── build.sh                       # Build script for local development
│   ├── test.sh                        # Test runner for all test suites
│   ├── docker-build.sh                # Docker image build script
│   └── setup-aws.sh                   # AWS SQS FIFO queue setup script
├── deployments/
│   ├── Dockerfile                     # Multi-stage Docker build
│   ├── docker-compose.yml             # Local development environment
│   ├── docker-compose.prod.yml        # Production deployment configuration
│   └── .dockerignore                  # Docker build exclusions
├── docs/
│   ├── api.md                         # API documentation and examples
│   ├── deployment.md                  # Deployment instructions
│   └── development.md                 # Local development setup guide
├── .github/
│   └── workflows/
│       ├── ci.yml                     # GitHub Actions CI/CD pipeline
│       └── release.yml                # Release and Docker image publishing
├── go.mod                             # Go module definition with dependencies
├── go.sum                             # Go module checksums
├── README.md                          # Project overview and quick start
├── Makefile                           # Build and development commands
└── .env.example                       # Example environment variables
```
