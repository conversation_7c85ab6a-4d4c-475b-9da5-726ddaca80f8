# REST API Spec

```yaml
openapi: 3.0.0
info:
  title: Go SQS API
  version: 1.0.0
  description: HTTP-to-SQS bridge API for reliable FIFO message queuing
servers:
  - url: http://localhost:8080
    description: Local development server
  - url: https://api.example.com
    description: Production server

paths:
  /health:
    get:
      summary: Health check endpoint
      description: Returns service health status for container orchestration
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "healthy"
                  uptime:
                    type: string
                    example: "2h30m45s"
                  version:
                    type: string
                    example: "1.0.0"

  /queue:
    post:
      summary: Submit message to SQS FIFO queue
      description: Accepts JSON payload and forwards to AWS SQS FIFO queue
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              description: Any valid JSON object
              example:
                orderId: "12345"
                customerId: "user-abc"
                items: ["item1", "item2"]
      responses:
        '200':
          description: Message successfully queued
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
              example:
                success: true
                message: "Message queued successfully"
                data:
                  messageId: "12345678-1234-1234-1234-123456789012"
                requestId: "req-abc123"
        '400':
          description: Invalid JSON payload
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                success: false
                message: "Invalid JSON payload"
                error: "unexpected end of JSON input"
                requestId: "req-def456"
        '500':
          description: AWS SQS service error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                success: false
                message: "Queue service unavailable"
                error: "AWS SQS connection timeout"
                requestId: "req-ghi789"

  /read:
    get:
      summary: Retrieve message from SQS FIFO queue
      description: Gets the next available message in FIFO order and removes it from queue
      responses:
        '200':
          description: Message retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MessageResponse'
              example:
                success: true
                message: "Message retrieved successfully"
                data:
                  messageId: "12345678-1234-1234-1234-123456789012"
                  body:
                    orderId: "12345"
                    customerId: "user-abc"
                    items: ["item1", "item2"]
                  timestamp: "2025-08-31T10:30:45Z"
                requestId: "req-jkl012"
        '204':
          description: No messages available in queue
        '500':
          description: AWS SQS service error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                success: false
                message: "Queue service unavailable"
                error: "Failed to receive messages from SQS"
                requestId: "req-mno345"

components:
  schemas:
    SuccessResponse:
      type: object
      required:
        - success
        - message
        - requestId
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Operation completed successfully"
        data:
          type: object
          description: Optional response payload
        requestId:
          type: string
          example: "req-abc123"

    ErrorResponse:
      type: object
      required:
        - success
        - message
        - requestId
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Operation failed"
        error:
          type: string
          example: "Detailed error information"
        requestId:
          type: string
          example: "req-def456"

    MessageResponse:
      type: object
      required:
        - success
        - message
        - data
        - requestId
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Message retrieved successfully"
        data:
          type: object
          required:
            - messageId
            - body
            - timestamp
          properties:
            messageId:
              type: string
              example: "12345678-1234-1234-1234-123456789012"
            body:
              type: object
              description: Original JSON payload from queue submission
            timestamp:
              type: string
              format: date-time
              example: "2025-08-31T10:30:45Z"
        requestId:
          type: string
          example: "req-jkl012"
```
