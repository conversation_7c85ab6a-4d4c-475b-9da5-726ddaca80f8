# Coding Standards

## Core Standards
- **Languages & Runtimes:** Go 1.21.12 (pin exact version), AWS SDK for Go v2 1.30.3
- **Style & Linting:** Standard `go fmt`, `golangci-lint` with default ruleset
- **Test Organization:** `*_test.go` files co-located with source, integration tests in `test/integration/`

## Naming Conventions
| Element | Convention | Example |
|---------|------------|---------|
| Interfaces | Noun + "er" suffix | `SQSServicer`, `Configurer` |
| HTTP Handlers | HTTP method + resource | `HandlePostQueue`, `HandleGetRead` |
| SQS Operations | Action + "Message" | `SendMessage`, `ReceiveMessage` |

## Critical Rules

- **Never use console.log equivalents:** Always use structured Zap logger with appropriate level
- **All HTTP responses must use APIResponse wrapper:** Ensures consistent JSON response format across endpoints
- **Environment variables must be validated on startup:** Service should fail fast with clear error messages for missing config
- **AWS operations must include context with timeout:** Prevent indefinite blocking on AWS service calls
- **All public functions must have Go doc comments:** Essential for AI agent code understanding
- **Error messages must never expose internal details:** Generic client messages, detailed server logging
- **JSON validation must happen at HTTP boundary:** Validate and sanitize all incoming payloads before processing
- **SQS MessageGroupId must be consistent:** Use single group ID for strict FIFO ordering requirement
