# Error Handling Strategy

## General Approach
- **Error Model:** Go standard error interface with structured error types for different failure categories
- **Exception Hierarchy:** Custom error types: `ValidationError`, `SQSServiceError`, `ConfigurationError`
- **Error Propagation:** Errors bubble up through service layers with context preservation, HTTP handlers convert to appropriate status codes

## Logging Standards
- **Library:** go.uber.org/zap v1.27.0
- **Format:** Structured JSON for production, human-readable for development
- **Levels:** DEBUG (development only), INFO (successful operations), WARN (retryable errors), ERROR (service failures)
- **Required Context:**
  - Correlation ID: UUID v4 format per request (`req-{uuid}`)
  - Service Context: service name, version, instance ID
  - User Context: No PII logged, only request correlation for debugging

## Error Handling Patterns

### External API Errors
- **Retry Policy:** Exponential backoff (100ms, 200ms, 400ms) for AWS SDK retryable errors
- **Circuit Breaker:** AWS SDK built-in circuit breaker with 30-second timeout
- **Timeout Configuration:** 5-second SQS operation timeout, 30-second total request timeout
- **Error Translation:** AWS SDK errors mapped to HTTP status codes (400 for client errors, 500 for service errors)

### Business Logic Errors
- **Custom Exceptions:** `InvalidJSONError`, `EmptyQueueError`, `ConfigurationMissingError`
- **User-Facing Errors:** Generic error messages with detailed logging for debugging
- **Error Codes:** HTTP status codes with structured JSON error responses

### Data Consistency
- **Transaction Strategy:** Single SQS operation per HTTP request (no distributed transactions needed)
- **Compensation Logic:** Not applicable - SQS handles message delivery guarantees
- **Idempotency:** SQS FIFO MessageDeduplicationId provides natural deduplication
