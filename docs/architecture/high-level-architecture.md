# High Level Architecture

## Technical Summary

The Go SQS API implements a **stateless microservice architecture** using Go's native HTTP capabilities to create an HTTP-to-SQS bridge. The system follows a **simple layered architecture** with HTTP handlers, business logic, and AWS service integration layers. Core technology choices include **Go 1.21+ with standard library HTTP server**, **AWS SDK for Go v2** for SQS FIFO queue operations, and **Alpine Linux containerization** for lightweight deployment. The architecture prioritizes **performance** (sub-100ms response times), **reliability** (99.9% uptime), and **scalability** (1000+ concurrent requests) while maintaining minimal operational complexity through stateless design and leveraging AWS SQS's managed reliability.

## High Level Overview

1. **Architectural Style:** **Stateless Microservice** - Single focused service with no persistent state, enabling horizontal scaling and container orchestration
2. **Repository Structure:** **Monorepo** - All application code, Docker configuration, and documentation in single repository as specified in PRD
3. **Service Architecture:** **Single Monolith Service** - The limited scope (two endpoints) doesn't warrant microservices complexity per PRD technical assumptions  
4. **Primary User Flow:** Applications submit JSON → HTTP POST /queue → AWS SQS FIFO → Applications retrieve via HTTP GET /read → Messages processed in FIFO order
5. **Key Architectural Decisions:**
   - **Stateless Design:** No local persistence enables easy containerization and scaling
   - **Direct AWS Integration:** Leverages SQS FIFO for ordering and reliability guarantees
   - **Standard Library First:** Minimal dependencies reduce attack surface and container size
   - **Environment-based Configuration:** 12-factor app principles for containerized deployment

## High Level Project Diagram

```mermaid
graph TD
    A[Client Applications] -->|HTTP POST /queue| B[Go SQS API Service]
    A -->|HTTP GET /read| B
    A -->|HTTP GET /health| B
    
    B -->|Send Message| C[AWS SQS FIFO Queue]
    B -->|Receive/Delete Message| C
    
    B -->|Logs| D[Container Logs/stdout]
    
    E[Environment Variables] -->|Config| B
    E -->|AWS Credentials| B
    
    F[Docker Container] -.->|Contains| B
    G[Container Platform] -.->|Orchestrates| F
    
    style B fill:#e1f5fe
    style C fill:#fff3e0
    style F fill:#f3e5f5
```

## Architectural and Design Patterns

**Selected Patterns:**

- **HTTP Handler Pattern:** Standard Go HTTP handler functions for endpoint routing - _Rationale:_ Leverages Go's excellent standard library HTTP capabilities, minimal dependencies, high performance
- **Repository Pattern:** Abstract SQS operations behind interface - _Rationale:_ Enables testing with mock implementations and future queue provider flexibility  
- **Dependency Injection:** Constructor injection for AWS clients and configuration - _Rationale:_ Supports testing, configuration management, and clean separation of concerns
- **Circuit Breaker (Lightweight):** Graceful degradation for AWS service unavailability - _Rationale:_ PRD requires handling of AWS SQS unavailability with appropriate error responses
- **Request-Response Pattern:** Synchronous HTTP API with async queue operations - _Rationale:_ Matches expected API behavior while leveraging SQS's asynchronous nature
- **Health Check Pattern:** Dedicated endpoint for container orchestration - _Rationale:_ Required for production deployment and container platform integration
