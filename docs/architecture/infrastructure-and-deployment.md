# Infrastructure and Deployment

## Infrastructure as Code
- **Tool:** Docker Compose v2.21+
- **Location:** `deployments/docker-compose.yml` and `deployments/docker-compose.prod.yml`
- **Approach:** Container-first deployment with environment-specific configurations

## Deployment Strategy
- **Strategy:** Blue-Green deployment with rolling updates for zero-downtime
- **CI/CD Platform:** GitHub Actions with Docker Hub registry
- **Pipeline Configuration:** `.github/workflows/ci.yml` and `.github/workflows/release.yml`

## Environments

- **Development:** Local Docker Compose with LocalStack SQS emulator - Hot reload enabled, debug logging
- **Staging:** Container deployment with dedicated AWS SQS test queue - Production-like environment for integration testing
- **Production:** Multi-replica container deployment with AWS SQS FIFO production queue - Auto-scaling, health checks, monitoring

## Environment Promotion Flow

```text
Development (Local)
    ↓ (git push to feature branch)
Automated Testing (GitHub Actions)
    ↓ (merge to main branch)
Staging Deployment (Auto-deploy)
    ↓ (manual approval after testing)
Production Deployment (Tagged release)
    ↓ (health checks pass)
Live Traffic (Blue-green cutover)
```

## Rollback Strategy
- **Primary Method:** Container registry rollback to previous image tag with health check validation
- **Trigger Conditions:** Health check failures, error rate > 5%, response time > 200ms sustained
- **Recovery Time Objective:** Under 5 minutes for container rollback, under 2 minutes for traffic cutover
