# Database Schema

This service is **stateless** and does not require a database. All message persistence and ordering is handled by **AWS SQS FIFO queues**.

**Rationale:**
- **AWS SQS FIFO** provides message persistence, ordering guarantees, and deduplication
- **Stateless design** enables horizontal scaling and container orchestration
- **No database complexity** reduces operational overhead and deployment requirements
- **Cloud-native approach** leverages AWS managed services for reliability and performance

**Message Storage:** All messages are stored in AWS SQS FIFO queue with:
- **Persistence:** Messages retained until consumed or TTL expires (default 14 days)
- **Ordering:** FIFO guarantee within MessageGroupId  
- **Deduplication:** 5-minute deduplication window based on MessageDeduplicationId
- **Reliability:** AWS SQS provides 99.9% availability SLA
