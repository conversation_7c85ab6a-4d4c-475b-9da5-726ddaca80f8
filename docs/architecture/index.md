# Go SQS API Architecture Document

## Table of Contents

- [Go SQS API Architecture Document](#table-of-contents)
  - [Introduction](./introduction.md)
    - [Starter Template or Existing Project](./introduction.md#starter-template-or-existing-project)
    - [Change Log](./introduction.md#change-log)
  - [High Level Architecture](./high-level-architecture.md)
    - [Technical Summary](./high-level-architecture.md#technical-summary)
    - [High Level Overview](./high-level-architecture.md#high-level-overview)
    - [High Level Project Diagram](./high-level-architecture.md#high-level-project-diagram)
    - [Architectural and Design Patterns](./high-level-architecture.md#architectural-and-design-patterns)
  - [Tech Stack](./tech-stack.md)
    - [Cloud Infrastructure](./tech-stack.md#cloud-infrastructure)
    - [Technology Stack Table](./tech-stack.md#technology-stack-table)
  - [Data Models](./data-models.md)
    - [SQSMessage](./data-models.md#sqsmessage)
    - [APIResponse](./data-models.md#apiresponse)
    - [Configuration](./data-models.md#configuration)
  - [Components](./components.md)
    - [HTTP Server](./components.md#http-server)
    - [SQS Service](./components.md#sqs-service)
    - [Configuration Manager](./components.md#configuration-manager)
    - [Logger Service](./components.md#logger-service)
    - [Component Diagrams](./components.md#component-diagrams)
  - [External APIs](./external-apis.md)
    - [AWS SQS API](./external-apis.md#aws-sqs-api)
  - [Core Workflows](./core-workflows.md)
  - [REST API Spec](./rest-api-spec.md)
  - [Database Schema](./database-schema.md)
  - [Source Tree](./source-tree.md)
  - [Infrastructure and Deployment](./infrastructure-and-deployment.md)
    - [Infrastructure as Code](./infrastructure-and-deployment.md#infrastructure-as-code)
    - [Deployment Strategy](./infrastructure-and-deployment.md#deployment-strategy)
    - [Environments](./infrastructure-and-deployment.md#environments)
    - [Environment Promotion Flow](./infrastructure-and-deployment.md#environment-promotion-flow)
    - [Rollback Strategy](./infrastructure-and-deployment.md#rollback-strategy)
  - [Error Handling Strategy](./error-handling-strategy.md)
    - [General Approach](./error-handling-strategy.md#general-approach)
    - [Logging Standards](./error-handling-strategy.md#logging-standards)
    - [Error Handling Patterns](./error-handling-strategy.md#error-handling-patterns)
      - [External API Errors](./error-handling-strategy.md#external-api-errors)
      - [Business Logic Errors](./error-handling-strategy.md#business-logic-errors)
      - [Data Consistency](./error-handling-strategy.md#data-consistency)
  - [Coding Standards](./coding-standards.md)
    - [Core Standards](./coding-standards.md#core-standards)
    - [Naming Conventions](./coding-standards.md#naming-conventions)
    - [Critical Rules](./coding-standards.md#critical-rules)
  - [Test Strategy and Standards](./test-strategy-and-standards.md)
    - [Testing Philosophy](./test-strategy-and-standards.md#testing-philosophy)
    - [Test Types and Organization](./test-strategy-and-standards.md#test-types-and-organization)
      - [Unit Tests](./test-strategy-and-standards.md#unit-tests)
      - [Integration Tests](./test-strategy-and-standards.md#integration-tests)
      - [End-to-End Tests](./test-strategy-and-standards.md#end-to-end-tests)
    - [Test Data Management](./test-strategy-and-standards.md#test-data-management)
    - [Continuous Testing](./test-strategy-and-standards.md#continuous-testing)
  - [Security](./security.md)
    - [Input Validation](./security.md#input-validation)
    - [Authentication & Authorization](./security.md#authentication-authorization)
    - [Secrets Management](./security.md#secrets-management)
    - [API Security](./security.md#api-security)
    - [Data Protection](./security.md#data-protection)
    - [Dependency Security](./security.md#dependency-security)
    - [Security Testing](./security.md#security-testing)
  - [Checklist Results Report](./checklist-results-report.md)
  - [Next Steps](./next-steps.md)
    - [For All Projects:](./next-steps.md#for-all-projects)
    - [Implementation Priority:](./next-steps.md#implementation-priority)
