# GoMAD Deployment Guide

This guide covers various deployment options for GoMAD in production environments.

## Prerequisites

- AWS Account with SQS access
- SQS FIFO queue created and configured
- AWS credentials configured
- Container runtime (Docker) or Go runtime

## AWS SQS Setup

### Create SQS FIFO Queue

1. **Via AWS Console:**
   - Navigate to SQS in AWS Console
   - Click "Create queue"
   - Select "FIFO" queue type
   - Name: `your-app-queue.fifo`
   - Configure settings:
     - Visibility timeout: 30 seconds
     - Message retention: 14 days
     - Receive message wait time: 20 seconds (long polling)
     - Content-based deduplication: Enabled

2. **Via AWS CLI:**
```bash
aws sqs create-queue \
  --queue-name your-app-queue.fifo \
  --attributes '{
    "FifoQueue": "true",
    "ContentBasedDeduplication": "true",
    "VisibilityTimeoutSeconds": "30",
    "MessageRetentionPeriod": "1209600",
    "ReceiveMessageWaitTimeSeconds": "20"
  }'
```

3. **Via Terraform:**
```hcl
resource "aws_sqs_queue" "gomad_queue" {
  name                        = "gomad-queue.fifo"
  fifo_queue                  = true
  content_based_deduplication = true
  visibility_timeout_seconds  = 30
  message_retention_seconds   = 1209600
  receive_wait_time_seconds   = 20

  tags = {
    Environment = "production"
    Application = "gomad"
  }
}
```

### IAM Role and Policy

Create an IAM role with the following policy:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "sqs:SendMessage",
        "sqs:ReceiveMessage",
        "sqs:DeleteMessage",
        "sqs:GetQueueAttributes"
      ],
      "Resource": "arn:aws:sqs:region:account-id:your-app-queue.fifo"
    }
  ]
}
```

## Docker Deployment

### Build Production Image

```dockerfile
# Multi-stage build for optimized production image
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main cmd/server/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/

COPY --from=builder /app/main .

EXPOSE 8080
CMD ["./main"]
```

### Build and Run

```bash
# Build image
docker build -t gomad:latest .

# Run container
docker run -d \
  --name gomad \
  -p 8080:8080 \
  -e AWS_REGION=us-east-1 \
  -e SQS_QUEUE_URL=https://sqs.us-east-1.amazonaws.com/************/your-app-queue.fifo \
  -e MESSAGE_GROUP_ID=production \
  -e LOG_LEVEL=info \
  -e PORT=8080 \
  --restart unless-stopped \
  gomad:latest
```

### Docker Compose

```yaml
version: '3.8'

services:
  gomad:
    build: .
    ports:
      - "8080:8080"
    environment:
      - AWS_REGION=us-east-1
      - SQS_QUEUE_URL=https://sqs.us-east-1.amazonaws.com/************/your-app-queue.fifo
      - MESSAGE_GROUP_ID=production
      - LOG_LEVEL=info
      - PORT=8080
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

## Kubernetes Deployment

### Namespace and ConfigMap

```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: gomad
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: gomad-config
  namespace: gomad
data:
  AWS_REGION: "us-east-1"
  MESSAGE_GROUP_ID: "production"
  LOG_LEVEL: "info"
  PORT: "8080"
```

### Secret for SQS Queue URL

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: gomad-secrets
  namespace: gomad
type: Opaque
data:
  sqs-queue-url: ********************************************************************************************
```

### Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gomad
  namespace: gomad
  labels:
    app: gomad
spec:
  replicas: 3
  selector:
    matchLabels:
      app: gomad
  template:
    metadata:
      labels:
        app: gomad
    spec:
      serviceAccountName: gomad-service-account
      containers:
      - name: gomad
        image: gomad:latest
        ports:
        - containerPort: 8080
          name: http
        envFrom:
        - configMapRef:
            name: gomad-config
        env:
        - name: SQS_QUEUE_URL
          valueFrom:
            secretKeyRef:
              name: gomad-secrets
              key: sqs-queue-url
        resources:
          requests:
            memory: "64Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          runAsNonRoot: true
          runAsUser: 1000
          capabilities:
            drop:
            - ALL
```

### Service and Ingress

```yaml
apiVersion: v1
kind: Service
metadata:
  name: gomad-service
  namespace: gomad
spec:
  selector:
    app: gomad
  ports:
  - port: 80
    targetPort: 8080
    protocol: TCP
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: gomad-ingress
  namespace: gomad
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/rate-limit: "1000"
spec:
  tls:
  - hosts:
    - api.yourdomain.com
    secretName: gomad-tls
  rules:
  - host: api.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: gomad-service
            port:
              number: 80
```

### ServiceAccount and RBAC

```yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: gomad-service-account
  namespace: gomad
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/gomad-eks-role
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: gomad
  name: gomad-role
rules:
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: gomad-rolebinding
  namespace: gomad
subjects:
- kind: ServiceAccount
  name: gomad-service-account
  namespace: gomad
roleRef:
  kind: Role
  name: gomad-role
  apiGroup: rbac.authorization.k8s.io
```

## AWS ECS Deployment

### Task Definition

```json
{
  "family": "gomad",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "256",
  "memory": "512",
  "executionRoleArn": "arn:aws:iam::************:role/ecsTaskExecutionRole",
  "taskRoleArn": "arn:aws:iam::************:role/gomadTaskRole",
  "containerDefinitions": [
    {
      "name": "gomad",
      "image": "************.dkr.ecr.us-east-1.amazonaws.com/gomad:latest",
      "portMappings": [
        {
          "containerPort": 8080,
          "protocol": "tcp"
        }
      ],
      "essential": true,
      "environment": [
        {
          "name": "AWS_REGION",
          "value": "us-east-1"
        },
        {
          "name": "MESSAGE_GROUP_ID",
          "value": "production"
        },
        {
          "name": "LOG_LEVEL",
          "value": "info"
        },
        {
          "name": "PORT",
          "value": "8080"
        }
      ],
      "secrets": [
        {
          "name": "SQS_QUEUE_URL",
          "valueFrom": "arn:aws:ssm:us-east-1:************:parameter/gomad/sqs-queue-url"
        }
      ],
      "healthCheck": {
        "command": [
          "CMD-SHELL",
          "wget --quiet --tries=1 --spider http://localhost:8080/health || exit 1"
        ],
        "interval": 30,
        "timeout": 5,
        "retries": 3,
        "startPeriod": 60
      },
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/gomad",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

### ECS Service

```json
{
  "serviceName": "gomad-service",
  "cluster": "production-cluster",
  "taskDefinition": "gomad:1",
  "desiredCount": 3,
  "launchType": "FARGATE",
  "networkConfiguration": {
    "awsvpcConfiguration": {
      "subnets": [
        "subnet-12345678",
        "subnet-87654321"
      ],
      "securityGroups": [
        "sg-12345678"
      ],
      "assignPublicIp": "DISABLED"
    }
  },
  "loadBalancers": [
    {
      "targetGroupArn": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/gomad-tg/************3456",
      "containerName": "gomad",
      "containerPort": 8080
    }
  ],
  "healthCheckGracePeriodSeconds": 60,
  "deploymentConfiguration": {
    "maximumPercent": 200,
    "minimumHealthyPercent": 50
  }
}
```

## Load Balancer Configuration

### Application Load Balancer (ALB)

```yaml
# Target Group
Type: application
Protocol: HTTP
Port: 8080
Health Check:
  Path: /health
  Interval: 30 seconds
  Timeout: 5 seconds
  Healthy threshold: 2
  Unhealthy threshold: 3

# Listener Rules
Port: 443 (HTTPS)
SSL Certificate: ACM certificate
Default action: Forward to gomad-target-group

Port: 80 (HTTP)
Default action: Redirect to HTTPS
```

### NGINX Configuration

```nginx
upstream gomad_backend {
    server gomad-1:8080 max_fails=3 fail_timeout=30s;
    server gomad-2:8080 max_fails=3 fail_timeout=30s;
    server gomad-3:8080 max_fails=3 fail_timeout=30s;
}

server {
    listen 80;
    server_name api.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.yourdomain.com;

    ssl_certificate /etc/ssl/certs/api.yourdomain.com.crt;
    ssl_certificate_key /etc/ssl/private/api.yourdomain.com.key;

    location / {
        proxy_pass http://gomad_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 5s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    location /health {
        proxy_pass http://gomad_backend/health;
        access_log off;
    }
}
```

## Monitoring and Logging

### CloudWatch Configuration

```yaml
# Log Group
LogGroup: /aws/ecs/gomad
RetentionInDays: 30

# Metrics
- RequestCount
- ResponseTime
- ErrorRate
- MemoryUtilization
- CPUUtilization

# Alarms
- ResponseTime > 100ms (P95)
- ErrorRate > 1%
- MemoryUtilization > 80%
- HealthCheckFailures > 2
```

### Prometheus Metrics

```yaml
# ServiceMonitor for Prometheus
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: gomad-metrics
  namespace: gomad
spec:
  selector:
    matchLabels:
      app: gomad
  endpoints:
  - port: http
    path: /metrics
    interval: 30s
```

## Security Considerations

### Network Security

1. **VPC Configuration:**
   - Deploy in private subnets
   - Use NAT Gateway for outbound internet access
   - Configure security groups with minimal required access

2. **Security Groups:**
   ```
   Inbound Rules:
   - Port 8080 from Load Balancer security group
   - Port 8080 from health check sources
   
   Outbound Rules:
   - Port 443 to AWS services (SQS)
   - Port 53 for DNS resolution
   ```

### Container Security

1. **Image Security:**
   - Use minimal base images (Alpine)
   - Scan images for vulnerabilities
   - Use specific version tags, not 'latest'

2. **Runtime Security:**
   - Run as non-root user
   - Drop all capabilities
   - Use read-only root filesystem where possible

### Secrets Management

1. **AWS Systems Manager Parameter Store:**
   ```bash
   aws ssm put-parameter \
     --name "/gomad/sqs-queue-url" \
     --value "https://sqs.us-east-1.amazonaws.com/************/queue.fifo" \
     --type "SecureString"
   ```

2. **Kubernetes Secrets:**
   ```bash
   kubectl create secret generic gomad-secrets \
     --from-literal=sqs-queue-url="https://sqs.us-east-1.amazonaws.com/************/queue.fifo" \
     --namespace=gomad
   ```

## Performance Tuning

### Container Resources

```yaml
resources:
  requests:
    memory: "64Mi"    # Minimum required
    cpu: "100m"       # 0.1 CPU core
  limits:
    memory: "256Mi"   # Maximum allowed
    cpu: "500m"       # 0.5 CPU core
```

### JVM-like Tuning for Go

```bash
# Environment variables for Go runtime
GOGC=100              # GC target percentage
GOMAXPROCS=2          # Max OS threads
GOMEMLIMIT=200MiB     # Memory limit
```

### Auto-scaling Configuration

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: gomad-hpa
  namespace: gomad
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: gomad
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

## Backup and Disaster Recovery

### SQS Message Backup

1. **Dead Letter Queue:**
   ```bash
   aws sqs create-queue \
     --queue-name gomad-dlq.fifo \
     --attributes '{
       "FifoQueue": "true",
       "MessageRetentionPeriod": "1209600"
     }'
   ```

2. **Redrive Policy:**
   ```json
   {
     "deadLetterTargetArn": "arn:aws:sqs:us-east-1:************:gomad-dlq.fifo",
     "maxReceiveCount": 3
   }
   ```

### Multi-Region Deployment

1. **Cross-Region Replication:**
   - Deploy identical infrastructure in multiple regions
   - Use Route 53 for DNS failover
   - Implement cross-region SQS message replication

2. **Database Backup:**
   - Regular snapshots of configuration
   - Automated backup of deployment manifests
   - Version control for infrastructure as code

## Troubleshooting

### Common Issues

1. **SQS Connection Issues:**
   ```bash
   # Test SQS connectivity
   aws sqs get-queue-attributes \
     --queue-url https://sqs.us-east-1.amazonaws.com/************/queue.fifo
   ```

2. **Health Check Failures:**
   ```bash
   # Check health endpoint
   curl -v http://localhost:8080/health
   
   # Check container logs
   docker logs gomad
   kubectl logs -f deployment/gomad -n gomad
   ```

3. **Performance Issues:**
   ```bash
   # Monitor resource usage
   docker stats gomad
   kubectl top pods -n gomad
   
   # Check application metrics
   curl http://localhost:8080/metrics
   ```

### Log Analysis

```bash
# Search for errors in logs
kubectl logs -f deployment/gomad -n gomad | grep ERROR

# Filter by request ID
kubectl logs deployment/gomad -n gomad | grep "req-a1b2c3d4"

# Monitor response times
kubectl logs deployment/gomad -n gomad | grep "duration" | tail -100
```
