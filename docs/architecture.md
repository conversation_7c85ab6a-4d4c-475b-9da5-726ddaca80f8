# Go SQS API Architecture Document

## Introduction

This document outlines the overall project architecture for **Go SQS API**, including backend systems, shared services, and non-UI specific concerns. Its primary goal is to serve as the guiding architectural blueprint for AI-driven development, ensuring consistency and adherence to chosen patterns and technologies.

**Relationship to Frontend Architecture:**
This is a pure API service project with no traditional frontend components. The system exposes HTTP REST endpoints for integration with other applications and services.

### Starter Template or Existing Project

Based on the PRD analysis, this is a greenfield Go project that will be built from scratch without a starter template. The PRD specifies using Go 1.21+ with standard library HTTP handling or lightweight frameworks, AWS SDK for Go v2, and Alpine Linux for containerization.

**Decision:** No starter template - building from scratch with Go standard library foundation.
**Rationale:** The focused scope (two HTTP endpoints + SQS integration) doesn't require complex framework scaffolding. Starting from scratch allows for minimal dependencies and optimal container size.

### Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-08-31 | 1.0 | Initial architecture document | <PERSON> (Architect) |

## High Level Architecture

### Technical Summary

The Go SQS API implements a **stateless microservice architecture** using Go's native HTTP capabilities to create an HTTP-to-SQS bridge. The system follows a **simple layered architecture** with HTTP handlers, business logic, and AWS service integration layers. Core technology choices include **Go 1.21+ with standard library HTTP server**, **AWS SDK for Go v2** for SQS FIFO queue operations, and **Alpine Linux containerization** for lightweight deployment. The architecture prioritizes **performance** (sub-100ms response times), **reliability** (99.9% uptime), and **scalability** (1000+ concurrent requests) while maintaining minimal operational complexity through stateless design and leveraging AWS SQS's managed reliability.

### High Level Overview

1. **Architectural Style:** **Stateless Microservice** - Single focused service with no persistent state, enabling horizontal scaling and container orchestration
2. **Repository Structure:** **Monorepo** - All application code, Docker configuration, and documentation in single repository as specified in PRD
3. **Service Architecture:** **Single Monolith Service** - The limited scope (two endpoints) doesn't warrant microservices complexity per PRD technical assumptions  
4. **Primary User Flow:** Applications submit JSON → HTTP POST /queue → AWS SQS FIFO → Applications retrieve via HTTP GET /read → Messages processed in FIFO order
5. **Key Architectural Decisions:**
   - **Stateless Design:** No local persistence enables easy containerization and scaling
   - **Direct AWS Integration:** Leverages SQS FIFO for ordering and reliability guarantees
   - **Standard Library First:** Minimal dependencies reduce attack surface and container size
   - **Environment-based Configuration:** 12-factor app principles for containerized deployment

### High Level Project Diagram

```mermaid
graph TD
    A[Client Applications] -->|HTTP POST /queue| B[Go SQS API Service]
    A -->|HTTP GET /read| B
    A -->|HTTP GET /health| B
    
    B -->|Send Message| C[AWS SQS FIFO Queue]
    B -->|Receive/Delete Message| C
    
    B -->|Logs| D[Container Logs/stdout]
    
    E[Environment Variables] -->|Config| B
    E -->|AWS Credentials| B
    
    F[Docker Container] -.->|Contains| B
    G[Container Platform] -.->|Orchestrates| F
    
    style B fill:#e1f5fe
    style C fill:#fff3e0
    style F fill:#f3e5f5
```

### Architectural and Design Patterns

**Selected Patterns:**

- **HTTP Handler Pattern:** Standard Go HTTP handler functions for endpoint routing - _Rationale:_ Leverages Go's excellent standard library HTTP capabilities, minimal dependencies, high performance
- **Repository Pattern:** Abstract SQS operations behind interface - _Rationale:_ Enables testing with mock implementations and future queue provider flexibility  
- **Dependency Injection:** Constructor injection for AWS clients and configuration - _Rationale:_ Supports testing, configuration management, and clean separation of concerns
- **Circuit Breaker (Lightweight):** Graceful degradation for AWS service unavailability - _Rationale:_ PRD requires handling of AWS SQS unavailability with appropriate error responses
- **Request-Response Pattern:** Synchronous HTTP API with async queue operations - _Rationale:_ Matches expected API behavior while leveraging SQS's asynchronous nature
- **Health Check Pattern:** Dedicated endpoint for container orchestration - _Rationale:_ Required for production deployment and container platform integration

## Tech Stack

### Cloud Infrastructure
- **Provider:** Amazon Web Services (AWS)
- **Key Services:** SQS FIFO, IAM for credentials, CloudWatch for monitoring
- **Deployment Regions:** Configurable via environment (recommend us-east-1 for cost/latency)

### Technology Stack Table

| Category | Technology | Version | Purpose | Rationale |
|----------|------------|---------|---------|-----------|
| **Language** | Go | 1.21.12 | Primary development language | LTS version, excellent concurrency, AWS SDK support, PRD requirement |
| **Runtime** | Go Runtime | 1.21.12 | Application runtime | Matches language version, stable performance |
| **HTTP Framework** | net/http (stdlib) | 1.21.12 | HTTP server and routing | Zero external dependencies, high performance, PRD emphasizes minimal footprint |
| **AWS SDK** | AWS SDK for Go v2 | 1.30.3 | SQS FIFO operations | Official AWS SDK, latest stable, excellent FIFO support |
| **Logging** | go.uber.org/zap | v1.27.0 | Structured logging | Zero-allocation performance, critical for 100ms SLA and 1000+ req/s targets |
| **Testing** | testing + testify | v1.9.0 | Unit and integration tests | Standard library + common assertions, PRD requires comprehensive testing |
| **Container Base** | Alpine Linux | 3.19 | Container base image | Minimal size, security-focused, PRD requirement |
| **Container Runtime** | Docker | 24.x+ | Containerization | Industry standard, PRD requirement for consistent deployment |
| **Configuration** | Environment Variables | - | Application configuration | 12-factor app compliance, container-friendly |

## Data Models

### SQSMessage

**Purpose:** Represents messages flowing through the system between HTTP API and AWS SQS FIFO queue

**Key Attributes:**
- **ID**: string - AWS SQS MessageId for tracking and deduplication  
- **Body**: json.RawMessage - Original JSON payload from HTTP POST request
- **MessageGroupId**: string - FIFO queue grouping identifier for ordering
- **MessageDeduplicationId**: string - FIFO queue deduplication identifier
- **Timestamp**: time.Time - Message creation timestamp for debugging
- **ReceiptHandle**: string - AWS SQS receipt handle for message deletion

**Relationships:**
- Maps 1:1 with AWS SQS FIFO messages
- Contains original HTTP request payload as JSON

### APIResponse

**Purpose:** Standardized HTTP response structure for consistent client interaction

**Key Attributes:**
- **Success**: bool - Operation success indicator
- **Message**: string - Human-readable operation result or error description
- **Data**: interface{} - Optional response payload (message content for GET /read)
- **Error**: string - Detailed error information when Success=false
- **RequestID**: string - Correlation ID for debugging and logging

**Relationships:**
- Used by all HTTP endpoints for consistent response format
- Links to logging via RequestID for debugging

### Configuration

**Purpose:** Application configuration structure loaded from environment variables

**Key Attributes:**
- **Port**: int - HTTP server port (default 8080)
- **AWSRegion**: string - AWS region for SQS operations  
- **SQSQueueURL**: string - Complete FIFO queue URL for message operations
- **LogLevel**: string - Zap logging level (debug, info, warn, error)
- **MessageGroupID**: string - FIFO queue message group identifier strategy

**Relationships:**
- Drives AWS SDK client initialization
- Controls application behavior and external service connections

## Components

### HTTP Server

**Responsibility:** HTTP request handling, routing, middleware, and response formatting

**Key Interfaces:**
- `POST /queue` - Accept JSON payloads for queue submission
- `GET /read` - Retrieve messages from queue in FIFO order  
- `GET /health` - Health check for container orchestration

**Dependencies:** Configuration, SQS Service, Logger

**Technology Stack:** Go net/http stdlib, custom middleware for logging/CORS, Zap logger integration

### SQS Service

**Responsibility:** AWS SQS FIFO queue operations, message formatting, and error handling

**Key Interfaces:**
- `SendMessage(body json.RawMessage) (*SQSMessage, error)` - Send to FIFO queue
- `ReceiveMessage() (*SQSMessage, error)` - Receive from FIFO queue with auto-delete
- `GenerateMessageIDs() (groupId, deduplicationId string)` - FIFO ID generation

**Dependencies:** AWS SDK v2 SQS Client, Configuration, Logger

**Technology Stack:** AWS SDK for Go v2, SQS FIFO-specific operations, circuit breaker pattern

### Configuration Manager

**Responsibility:** Environment variable loading, validation, and application configuration

**Key Interfaces:**
- `LoadConfig() (*Configuration, error)` - Load and validate environment configuration
- `GetAWSConfig() aws.Config` - AWS SDK configuration with credentials
- `Validate() error` - Configuration validation and required field checking

**Dependencies:** Environment Variables, AWS SDK Config

**Technology Stack:** Go stdlib env parsing, AWS SDK config loading, validation logic

### Logger Service

**Responsibility:** Structured logging, request correlation, and observability

**Key Interfaces:**
- `WithRequestID(id string) Logger` - Add request correlation
- `LogAPIRequest(method, path string, duration time.Duration)` - HTTP request logging
- `LogSQSOperation(operation string, success bool, error)` - SQS operation logging

**Dependencies:** Zap logger, Configuration

**Technology Stack:** Zap structured logger, JSON output format, configurable log levels

### Component Diagrams

```mermaid
graph TB
    subgraph "Go SQS API Service"
        A[HTTP Server] --> B[SQS Service]
        A --> D[Logger Service]
        B --> D
        C[Configuration Manager] --> A
        C --> B
        C --> D
        
        A --> E["/queue Handler"]
        A --> F["/read Handler"] 
        A --> G["/health Handler"]
        
        B --> H[AWS SQS Client]
        C --> I[Environment Variables]
        D --> J[Structured JSON Logs]
    end
    
    K[Client Applications] --> A
    H --> L[AWS SQS FIFO Queue]
    J --> M[Container Log Aggregation]
    
    style A fill:#e3f2fd
    style B fill:#fff3e0  
    style C fill:#f1f8e9
    style D fill:#fce4ec
```

## External APIs

### AWS SQS API

- **Purpose:** FIFO queue operations for message ordering and reliable delivery
- **Documentation:** https://docs.aws.amazon.com/sqs/latest/APIReference/
- **Base URL(s):** https://sqs.{region}.amazonaws.com/{account-id}/{queue-name}.fifo
- **Authentication:** AWS Signature Version 4 with IAM credentials (Access Key/Secret or IAM Role)
- **Rate Limits:** 300 API calls per second for FIFO queues, 120,000 inflight messages maximum

**Key Endpoints Used:**
- `POST {queue-url}` - SendMessage action for queue submission with MessageGroupId/MessageDeduplicationId
- `POST {queue-url}` - ReceiveMessage action for message retrieval with MaxNumberOfMessages=1
- `POST {queue-url}` - DeleteMessage action for message cleanup after successful processing

**Integration Notes:** AWS SDK for Go v2 handles authentication, retries, and error handling. FIFO queues require MessageGroupId for ordering and MessageDeduplicationId for deduplication. Service uses single message group for strict FIFO ordering across all messages.

## Core Workflows

```mermaid
sequenceDiagram
    participant C as Client App
    participant H as HTTP Server
    participant S as SQS Service
    participant Q as AWS SQS FIFO
    participant L as Logger

    Note over C,L: Message Submission Workflow
    C->>H: POST /queue {json payload}
    H->>L: Log incoming request
    H->>H: Validate JSON payload
    H->>S: SendMessage(json)
    S->>S: Generate MessageGroupId & DeduplicationId
    S->>Q: SendMessage with FIFO metadata
    Q-->>S: MessageId response
    S-->>H: Success with MessageId
    H->>L: Log successful submission
    H-->>C: HTTP 200 {success: true, messageId}

    Note over C,L: Message Retrieval Workflow  
    C->>H: GET /read
    H->>L: Log retrieval request
    H->>S: ReceiveMessage()
    S->>Q: ReceiveMessage(MaxMessages=1)
    
    alt Message Available
        Q-->>S: Message with ReceiptHandle
        S->>Q: DeleteMessage(ReceiptHandle)
        Q-->>S: Delete confirmation
        S-->>H: Message data
        H->>L: Log successful retrieval
        H-->>C: HTTP 200 {message data}
    else Queue Empty
        Q-->>S: No messages available
        S-->>H: Empty result
        H->>L: Log empty queue
        H-->>C: HTTP 204 No Content
    end

    Note over C,L: Error Handling Workflow
    C->>H: POST /queue {invalid json}
    H->>H: JSON validation fails
    H->>L: Log validation error
    H-->>C: HTTP 400 {error details}
    
    C->>H: GET /read
    H->>S: ReceiveMessage()
    S->>Q: ReceiveMessage fails (AWS unavailable)
    Q-->>S: Service error
    S-->>H: Error with retry info
    H->>L: Log AWS service error
    H-->>C: HTTP 500 {service unavailable}
```

## REST API Spec

```yaml
openapi: 3.0.0
info:
  title: Go SQS API
  version: 1.0.0
  description: HTTP-to-SQS bridge API for reliable FIFO message queuing
servers:
  - url: http://localhost:8080
    description: Local development server
  - url: https://api.example.com
    description: Production server

paths:
  /health:
    get:
      summary: Health check endpoint
      description: Returns service health status for container orchestration
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "healthy"
                  uptime:
                    type: string
                    example: "2h30m45s"
                  version:
                    type: string
                    example: "1.0.0"

  /queue:
    post:
      summary: Submit message to SQS FIFO queue
      description: Accepts JSON payload and forwards to AWS SQS FIFO queue
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              description: Any valid JSON object
              example:
                orderId: "12345"
                customerId: "user-abc"
                items: ["item1", "item2"]
      responses:
        '200':
          description: Message successfully queued
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
              example:
                success: true
                message: "Message queued successfully"
                data:
                  messageId: "12345678-1234-1234-1234-123456789012"
                requestId: "req-abc123"
        '400':
          description: Invalid JSON payload
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                success: false
                message: "Invalid JSON payload"
                error: "unexpected end of JSON input"
                requestId: "req-def456"
        '500':
          description: AWS SQS service error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                success: false
                message: "Queue service unavailable"
                error: "AWS SQS connection timeout"
                requestId: "req-ghi789"

  /read:
    get:
      summary: Retrieve message from SQS FIFO queue
      description: Gets the next available message in FIFO order and removes it from queue
      responses:
        '200':
          description: Message retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MessageResponse'
              example:
                success: true
                message: "Message retrieved successfully"
                data:
                  messageId: "12345678-1234-1234-1234-123456789012"
                  body:
                    orderId: "12345"
                    customerId: "user-abc"
                    items: ["item1", "item2"]
                  timestamp: "2025-08-31T10:30:45Z"
                requestId: "req-jkl012"
        '204':
          description: No messages available in queue
        '500':
          description: AWS SQS service error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                success: false
                message: "Queue service unavailable"
                error: "Failed to receive messages from SQS"
                requestId: "req-mno345"

components:
  schemas:
    SuccessResponse:
      type: object
      required:
        - success
        - message
        - requestId
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Operation completed successfully"
        data:
          type: object
          description: Optional response payload
        requestId:
          type: string
          example: "req-abc123"

    ErrorResponse:
      type: object
      required:
        - success
        - message
        - requestId
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Operation failed"
        error:
          type: string
          example: "Detailed error information"
        requestId:
          type: string
          example: "req-def456"

    MessageResponse:
      type: object
      required:
        - success
        - message
        - data
        - requestId
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Message retrieved successfully"
        data:
          type: object
          required:
            - messageId
            - body
            - timestamp
          properties:
            messageId:
              type: string
              example: "12345678-1234-1234-1234-123456789012"
            body:
              type: object
              description: Original JSON payload from queue submission
            timestamp:
              type: string
              format: date-time
              example: "2025-08-31T10:30:45Z"
        requestId:
          type: string
          example: "req-jkl012"
```

## Database Schema

This service is **stateless** and does not require a database. All message persistence and ordering is handled by **AWS SQS FIFO queues**.

**Rationale:**
- **AWS SQS FIFO** provides message persistence, ordering guarantees, and deduplication
- **Stateless design** enables horizontal scaling and container orchestration
- **No database complexity** reduces operational overhead and deployment requirements
- **Cloud-native approach** leverages AWS managed services for reliability and performance

**Message Storage:** All messages are stored in AWS SQS FIFO queue with:
- **Persistence:** Messages retained until consumed or TTL expires (default 14 days)
- **Ordering:** FIFO guarantee within MessageGroupId  
- **Deduplication:** 5-minute deduplication window based on MessageDeduplicationId
- **Reliability:** AWS SQS provides 99.9% availability SLA

## Source Tree

```plaintext
gomad/
├── cmd/
│   └── server/
│       └── main.go                    # Application entry point with server startup
├── internal/
│   ├── config/
│   │   ├── config.go                  # Configuration struct and environment loading
│   │   └── config_test.go             # Configuration validation tests
│   ├── handlers/
│   │   ├── health.go                  # GET /health endpoint handler
│   │   ├── queue.go                   # POST /queue endpoint handler
│   │   ├── read.go                    # GET /read endpoint handler
│   │   ├── middleware.go              # Request logging and CORS middleware
│   │   └── handlers_test.go           # HTTP handler unit tests
│   ├── services/
│   │   ├── sqs.go                     # SQS service implementation
│   │   ├── sqs_interface.go           # SQS service interface for testing
│   │   └── sqs_test.go                # SQS service unit tests
│   ├── models/
│   │   ├── api.go                     # APIResponse and request/response structs
│   │   ├── message.go                 # SQSMessage struct and methods
│   │   └── models_test.go             # Model validation tests
│   └── logger/
│       ├── logger.go                  # Zap logger setup and structured logging
│       └── logger_test.go             # Logger configuration tests
├── test/
│   ├── integration/
│   │   ├── api_test.go                # Full API integration tests with test SQS
│   │   ├── docker-compose.test.yml    # Test environment with LocalStack SQS
│   │   └── testdata/
│   │       ├── valid_messages.json    # Test message payloads
│   │       └── invalid_messages.json  # Invalid payload test cases
│   └── mocks/
│       └── sqs_mock.go                # Mock SQS service for unit tests
├── scripts/
│   ├── build.sh                       # Build script for local development
│   ├── test.sh                        # Test runner for all test suites
│   ├── docker-build.sh                # Docker image build script
│   └── setup-aws.sh                   # AWS SQS FIFO queue setup script
├── deployments/
│   ├── Dockerfile                     # Multi-stage Docker build
│   ├── docker-compose.yml             # Local development environment
│   ├── docker-compose.prod.yml        # Production deployment configuration
│   └── .dockerignore                  # Docker build exclusions
├── docs/
│   ├── api.md                         # API documentation and examples
│   ├── deployment.md                  # Deployment instructions
│   └── development.md                 # Local development setup guide
├── .github/
│   └── workflows/
│       ├── ci.yml                     # GitHub Actions CI/CD pipeline
│       └── release.yml                # Release and Docker image publishing
├── go.mod                             # Go module definition with dependencies
├── go.sum                             # Go module checksums
├── README.md                          # Project overview and quick start
├── Makefile                           # Build and development commands
└── .env.example                       # Example environment variables
```

## Infrastructure and Deployment

### Infrastructure as Code
- **Tool:** Docker Compose v2.21+
- **Location:** `deployments/docker-compose.yml` and `deployments/docker-compose.prod.yml`
- **Approach:** Container-first deployment with environment-specific configurations

### Deployment Strategy
- **Strategy:** Blue-Green deployment with rolling updates for zero-downtime
- **CI/CD Platform:** GitHub Actions with Docker Hub registry
- **Pipeline Configuration:** `.github/workflows/ci.yml` and `.github/workflows/release.yml`

### Environments

- **Development:** Local Docker Compose with LocalStack SQS emulator - Hot reload enabled, debug logging
- **Staging:** Container deployment with dedicated AWS SQS test queue - Production-like environment for integration testing
- **Production:** Multi-replica container deployment with AWS SQS FIFO production queue - Auto-scaling, health checks, monitoring

### Environment Promotion Flow

```text
Development (Local)
    ↓ (git push to feature branch)
Automated Testing (GitHub Actions)
    ↓ (merge to main branch)
Staging Deployment (Auto-deploy)
    ↓ (manual approval after testing)
Production Deployment (Tagged release)
    ↓ (health checks pass)
Live Traffic (Blue-green cutover)
```

### Rollback Strategy
- **Primary Method:** Container registry rollback to previous image tag with health check validation
- **Trigger Conditions:** Health check failures, error rate > 5%, response time > 200ms sustained
- **Recovery Time Objective:** Under 5 minutes for container rollback, under 2 minutes for traffic cutover

## Error Handling Strategy

### General Approach
- **Error Model:** Go standard error interface with structured error types for different failure categories
- **Exception Hierarchy:** Custom error types: `ValidationError`, `SQSServiceError`, `ConfigurationError`
- **Error Propagation:** Errors bubble up through service layers with context preservation, HTTP handlers convert to appropriate status codes

### Logging Standards
- **Library:** go.uber.org/zap v1.27.0
- **Format:** Structured JSON for production, human-readable for development
- **Levels:** DEBUG (development only), INFO (successful operations), WARN (retryable errors), ERROR (service failures)
- **Required Context:**
  - Correlation ID: UUID v4 format per request (`req-{uuid}`)
  - Service Context: service name, version, instance ID
  - User Context: No PII logged, only request correlation for debugging

### Error Handling Patterns

#### External API Errors
- **Retry Policy:** Exponential backoff (100ms, 200ms, 400ms) for AWS SDK retryable errors
- **Circuit Breaker:** AWS SDK built-in circuit breaker with 30-second timeout
- **Timeout Configuration:** 5-second SQS operation timeout, 30-second total request timeout
- **Error Translation:** AWS SDK errors mapped to HTTP status codes (400 for client errors, 500 for service errors)

#### Business Logic Errors
- **Custom Exceptions:** `InvalidJSONError`, `EmptyQueueError`, `ConfigurationMissingError`
- **User-Facing Errors:** Generic error messages with detailed logging for debugging
- **Error Codes:** HTTP status codes with structured JSON error responses

#### Data Consistency
- **Transaction Strategy:** Single SQS operation per HTTP request (no distributed transactions needed)
- **Compensation Logic:** Not applicable - SQS handles message delivery guarantees
- **Idempotency:** SQS FIFO MessageDeduplicationId provides natural deduplication

## Coding Standards

### Core Standards
- **Languages & Runtimes:** Go 1.21.12 (pin exact version), AWS SDK for Go v2 1.30.3
- **Style & Linting:** Standard `go fmt`, `golangci-lint` with default ruleset
- **Test Organization:** `*_test.go` files co-located with source, integration tests in `test/integration/`

### Naming Conventions
| Element | Convention | Example |
|---------|------------|---------|
| Interfaces | Noun + "er" suffix | `SQSServicer`, `Configurer` |
| HTTP Handlers | HTTP method + resource | `HandlePostQueue`, `HandleGetRead` |
| SQS Operations | Action + "Message" | `SendMessage`, `ReceiveMessage` |

### Critical Rules

- **Never use console.log equivalents:** Always use structured Zap logger with appropriate level
- **All HTTP responses must use APIResponse wrapper:** Ensures consistent JSON response format across endpoints
- **Environment variables must be validated on startup:** Service should fail fast with clear error messages for missing config
- **AWS operations must include context with timeout:** Prevent indefinite blocking on AWS service calls
- **All public functions must have Go doc comments:** Essential for AI agent code understanding
- **Error messages must never expose internal details:** Generic client messages, detailed server logging
- **JSON validation must happen at HTTP boundary:** Validate and sanitize all incoming payloads before processing
- **SQS MessageGroupId must be consistent:** Use single group ID for strict FIFO ordering requirement

## Test Strategy and Standards

### Testing Philosophy
- **Approach:** Test-driven development with comprehensive coverage for critical paths (HTTP endpoints, SQS operations)
- **Coverage Goals:** 80% unit test coverage, 100% coverage for public APIs and error paths
- **Test Pyramid:** 70% unit tests, 25% integration tests, 5% end-to-end tests

### Test Types and Organization

#### Unit Tests
- **Framework:** testing (stdlib) + testify v1.9.0
- **File Convention:** `*_test.go` co-located with source files
- **Location:** Alongside source in `internal/` packages
- **Mocking Library:** `testify/mock` for interface mocking
- **Coverage Requirement:** 80% minimum for all packages

**AI Agent Requirements:**
- Generate tests for all public methods and HTTP handlers
- Cover edge cases and error conditions (invalid JSON, AWS failures, empty queues)
- Follow AAA pattern (Arrange, Act, Assert)
- Mock all external dependencies (AWS SDK, configuration)

#### Integration Tests
- **Scope:** Full API testing with real AWS SQS operations using test queue
- **Location:** `test/integration/` directory
- **Test Infrastructure:**
  - **AWS SQS:** Real FIFO test queue with dedicated AWS credentials
  - **HTTP Server:** Full server startup with test configuration
  - **Docker Compose:** LocalStack SQS emulator for offline testing

#### End-to-End Tests
- **Framework:** Go testing with HTTP client
- **Scope:** Complete user workflows (POST message → GET message verification)
- **Environment:** Containerized service with real SQS test queue
- **Test Data:** JSON fixtures in `test/integration/testdata/`

### Test Data Management
- **Strategy:** JSON fixture files with valid/invalid message examples
- **Fixtures:** `test/integration/testdata/` with categorized test cases
- **Factories:** Builder pattern for test message creation with randomized data
- **Cleanup:** Automatic SQS queue purging after each integration test suite

### Continuous Testing
- **CI Integration:** GitHub Actions runs unit tests on PR, integration tests on merge to main
- **Performance Tests:** Benchmark tests for critical paths (JSON parsing, SQS operations) 
- **Security Tests:** Go security scanner (`gosec`) in CI pipeline for vulnerability detection

## Security

### Input Validation
- **Validation Library:** Go standard library `json` package with custom validation functions
- **Validation Location:** HTTP handler level before business logic processing
- **Required Rules:**
  - All HTTP request bodies MUST be valid JSON (reject malformed payloads)
  - JSON payload size limited to 256KB maximum
  - Whitelist approach: reject requests with unexpected headers or methods

### Authentication & Authorization
- **Auth Method:** None required - stateless API service for internal/trusted network deployment
- **Session Management:** Not applicable - each HTTP request is independent
- **Required Patterns:**
  - Service deployed behind network security groups/firewall rules
  - Container network isolation in production deployments

### Secrets Management
- **Development:** Environment variables with `.env` file (excluded from git)
- **Production:** Container environment variables injected by orchestration platform
- **Code Requirements:**
  - NEVER hardcode AWS credentials or sensitive configuration
  - Access via `os.Getenv()` only with validation on startup
  - No secrets in logs, error messages, or debug output

### API Security
- **Rate Limiting:** Rely on container platform/load balancer rate limiting
- **CORS Policy:** Restrictive CORS headers - allow only necessary origins
- **Security Headers:** `X-Content-Type-Options: nosniff`, `X-Frame-Options: DENY`
- **HTTPS Enforcement:** TLS termination at load balancer/reverse proxy level

### Data Protection
- **Encryption at Rest:** AWS SQS server-side encryption (SSE-SQS) enabled on FIFO queue
- **Encryption in Transit:** HTTPS for API endpoints, AWS SDK uses TLS for SQS communication
- **PII Handling:** No PII processing - service is payload-agnostic message bridge
- **Logging Restrictions:** Never log message payloads, AWS credentials, or request bodies

### Dependency Security
- **Scanning Tool:** `go mod audit` and GitHub Dependabot for dependency vulnerabilities
- **Update Policy:** Monthly dependency updates with automated security patch application
- **Approval Process:** New dependencies require architecture review for minimal footprint principle

### Security Testing
- **SAST Tool:** `gosec` static analysis in CI pipeline for Go-specific vulnerabilities
- **DAST Tool:** Not applicable - internal API service without public endpoints
- **Penetration Testing:** Not required - focused internal service with limited attack surface

## Checklist Results Report

*This section will be populated after running the architect-checklist to validate the architecture document quality and completeness.*

## Next Steps

### For All Projects:
- Review architecture document with Product Owner and stakeholders
- Begin story implementation with Dev agent using this architecture as reference
- Set up development environment following source tree structure
- Initialize CI/CD pipeline based on deployment strategy
- Configure monitoring and logging for production deployment

### Implementation Priority:
1. **Foundation Setup** - Project structure, configuration, basic HTTP server
2. **Core Services** - SQS service implementation and integration
3. **API Endpoints** - HTTP handlers for /health, /queue, /read
4. **Testing Suite** - Unit, integration, and e2e test implementation  
5. **Containerization** - Docker build and deployment configuration
6. **Production Deployment** - CI/CD pipeline and environment setup

This architecture document serves as the definitive technical blueprint for the Go SQS API project and should be referenced by all development agents and team members.