# Development Guide

This document provides comprehensive guidance for developing, testing, and contributing to the Go SQS API project.

## Table of Contents

- [Development Setup](#development-setup)
- [Testing Framework](#testing-framework)
- [Running Tests](#running-tests)
- [Test Types](#test-types)
- [CI/CD Integration](#cicd-integration)
- [Performance Requirements](#performance-requirements)
- [Security Testing](#security-testing)
- [Contributing Guidelines](#contributing-guidelines)

## Development Setup

### Prerequisites

- Go 1.22 or later
- Docker and Docker Compose
- AWS CLI (for real AWS testing)
- Make (optional, for convenience targets)

### Local Development

1. **Clone and setup the project:**
   ```bash
   git clone <repository-url>
   cd gomad
   go mod download
   ```

2. **Build the application:**
   ```bash
   make build
   # or
   go build -o bin/server ./cmd/server
   ```

3. **Run locally:**
   ```bash
   make run
   # or
   ./bin/server
   ```

4. **Run with Docker:**
   ```bash
   make docker-build
   make docker-run
   ```

## Testing Framework

The project uses a comprehensive testing strategy with multiple test types:

### Test Structure

```
test/
├── integration/           # Integration tests
│   ├── api_test.go       # Main integration test suite
│   ├── testdata/         # Test data and builders
│   │   ├── valid_messages.json
│   │   ├── invalid_messages.json
│   │   └── builder.go
│   ├── docker-compose.test.yml  # LocalStack test environment
│   └── Dockerfile.test   # Test runner container
├── mocks/                # Mock implementations
│   └── sqs_mock.go      # SQS service mock
└── coverage/            # Coverage reports (generated)
    ├── unit.out
    ├── integration.out
    └── combined.html
```

### Test Categories

1. **Unit Tests** (70% of test suite)
   - Located alongside source code (`*_test.go`)
   - Mock all external dependencies
   - Fast execution (< 5 seconds)
   - 80%+ code coverage target

2. **Integration Tests** (25% of test suite)
   - Located in `test/integration/`
   - Test real AWS SQS or LocalStack
   - End-to-end workflows
   - Performance validation

3. **Security Tests** (5% of test suite)
   - Static analysis with gosec
   - Vulnerability scanning
   - Input validation testing

## Running Tests

### Quick Test Commands

```bash
# Run all tests
make test-all

# Run unit tests only
make test

# Run integration tests
make test-integration

# Run integration tests with LocalStack
make test-integration-localstack

# Run security scans
make test-security

# Run performance tests
make test-performance
```

### Using the Test Script

The `scripts/test.sh` script provides comprehensive testing options:

```bash
# Basic usage
./scripts/test.sh [test-type] [options]

# Examples
./scripts/test.sh unit -v -c                    # Unit tests with verbose output and coverage
./scripts/test.sh integration --localstack      # Integration tests with LocalStack
./scripts/test.sh all -v -c -t 60s              # All tests with coverage and 60s timeout
./scripts/test.sh security                      # Security scans only
```

### Test Options

- `-v, --verbose`: Enable verbose output
- `-c, --coverage`: Generate coverage reports
- `-t, --timeout`: Set test timeout (default: 30s)
- `-l, --localstack`: Use LocalStack for integration tests
- `-h, --help`: Show help message

## Test Types

### Unit Tests

Unit tests are located alongside source code and follow Go conventions:

```bash
# Run unit tests
go test ./internal/... ./cmd/...

# With coverage
go test -coverprofile=coverage.out ./internal/... ./cmd/...
go tool cover -html=coverage.out -o coverage.html
```

**Requirements:**
- Use testify for assertions
- Mock external dependencies
- Follow AAA pattern (Arrange, Act, Assert)
- Cover edge cases and error conditions

### Integration Tests

Integration tests validate end-to-end functionality:

```bash
# Run with real AWS (requires credentials)
USE_REAL_SQS=true go test ./test/integration/...

# Run with LocalStack
./scripts/test.sh integration --localstack
```

**Environment Variables:**
- `USE_REAL_SQS=true`: Use real AWS SQS
- `AWS_REGION`: AWS region (default: us-east-1)
- `SQS_QUEUE_URL`: SQS queue URL for testing
- `MESSAGE_GROUP_ID`: FIFO message group ID

### LocalStack Testing

For offline development and CI, use LocalStack:

```bash
# Start LocalStack test environment
cd test/integration
docker-compose -f docker-compose.test.yml up

# Run tests against LocalStack
USE_REAL_SQS=true AWS_ENDPOINT_URL=http://localhost:4566 go test ./test/integration/...
```

### Security Testing

Security tests use gosec for static analysis:

```bash
# Install gosec
go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest

# Run security scan
gosec ./...

# Generate JSON report
gosec -fmt json -out security-report.json ./...
```

### Performance Testing

Performance tests validate response times and throughput:

```bash
# Run benchmark tests
go test -bench=. -benchmem ./...

# Performance requirements
# - Health endpoint: < 10ms response time
# - API endpoints: < 100ms response time
# - Throughput: 1000+ requests/second
```

## CI/CD Integration

### GitHub Actions

The project includes GitHub Actions workflows for:

1. **Pull Request Checks:**
   - Unit tests
   - Security scans
   - Code formatting
   - Linting

2. **Main Branch Integration:**
   - Full test suite
   - Integration tests with LocalStack
   - Coverage reporting
   - Docker image building

### Environment Setup

For CI environments, set these environment variables:

```bash
# Required for integration tests
AWS_REGION=us-east-1
SQS_QUEUE_URL=http://localstack:4566/000000000000/test-queue.fifo
MESSAGE_GROUP_ID=ci-test-group
AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=test
USE_REAL_SQS=true
```

## Performance Requirements

### Response Time Targets

- **Health endpoint**: < 10ms
- **API endpoints**: < 100ms
- **SQS operations**: < 5 seconds

### Throughput Targets

- **Concurrent requests**: 1000+ req/s
- **Message processing**: 100+ msg/s
- **Memory usage**: < 256MB under load

### Load Testing

```bash
# Use Apache Bench for basic load testing
ab -n 1000 -c 10 http://localhost:8080/health

# Use custom performance tests
go test -bench=BenchmarkHealthEndpoint ./test/integration/...
```

## Security Testing

### Static Analysis

```bash
# Run gosec security scanner
gosec ./...

# Check for vulnerabilities in dependencies
go list -json -m all | nancy sleuth
```

### Input Validation Testing

The test suite includes validation for:
- Malformed JSON payloads
- SQL injection attempts
- XSS patterns
- Extremely large payloads
- Special characters and Unicode

### Security Requirements

- No hardcoded credentials
- Input sanitization
- Secure error handling
- Minimal attack surface

## Contributing Guidelines

### Code Standards

1. **Go Code Style:**
   - Follow `gofmt` formatting
   - Use `golangci-lint` for linting
   - Write clear, self-documenting code
   - Include comprehensive tests

2. **Testing Requirements:**
   - Unit tests for all public functions
   - Integration tests for API endpoints
   - Minimum 80% code coverage
   - Performance tests for critical paths

3. **Documentation:**
   - Update README for user-facing changes
   - Document API changes
   - Include inline code comments
   - Update this development guide

### Pull Request Process

1. **Before submitting:**
   ```bash
   make fmt          # Format code
   make lint         # Run linter
   make test-all     # Run all tests
   make ci           # Run CI pipeline locally
   ```

2. **PR Requirements:**
   - All tests passing
   - Code coverage maintained
   - Security scans clean
   - Documentation updated

3. **Review Process:**
   - Automated checks must pass
   - Code review by maintainers
   - Integration tests in CI
   - Performance validation

### Development Workflow

1. **Feature Development:**
   ```bash
   git checkout -b feature/your-feature
   # Develop and test
   make test-all
   git commit -m "feat: your feature description"
   git push origin feature/your-feature
   ```

2. **Bug Fixes:**
   ```bash
   git checkout -b fix/bug-description
   # Fix and test
   make test-all
   git commit -m "fix: bug description"
   git push origin fix/bug-description
   ```

3. **Testing Changes:**
   ```bash
   # Test locally
   make test-all

   # Test with LocalStack
   make test-integration-localstack

   # Test with real AWS (if available)
   USE_REAL_SQS=true make test-integration
   ```

## Troubleshooting

### Common Issues

1. **Tests failing with AWS credentials:**
   - Use LocalStack: `make test-integration-localstack`
   - Set test credentials: `AWS_ACCESS_KEY_ID=test AWS_SECRET_ACCESS_KEY=test`

2. **Docker build issues:**
   - Check Go version in Dockerfile
   - Verify .dockerignore excludes test files
   - Clean Docker cache: `docker system prune`

3. **Performance test failures:**
   - Check system load
   - Verify timeout settings
   - Run tests individually

### Getting Help

- Check existing issues in the repository
- Review test logs for detailed error messages
- Use verbose test output: `./scripts/test.sh all -v`
- Contact maintainers for complex issues

## Additional Resources

- [Go Testing Documentation](https://golang.org/pkg/testing/)
- [Testify Documentation](https://github.com/stretchr/testify)
- [LocalStack Documentation](https://docs.localstack.cloud/)
- [AWS SQS Documentation](https://docs.aws.amazon.com/sqs/)
- [Docker Compose Documentation](https://docs.docker.com/compose/)
