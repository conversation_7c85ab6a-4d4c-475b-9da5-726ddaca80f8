# Technical Assumptions

Based on the project requirements and constraints, the following technical decisions will guide the development:

## Repository Structure: Monorepo
- **Decision**: Single repository containing all application code, Docker configuration, and documentation
- **Rationale**: Simple deployment model for a focused API service with minimal complexity

## Service Architecture
- **Decision**: Single monolith service with stateless design
- **Rationale**: The limited scope (two endpoints) doesn't warrant microservices complexity. Stateless design enables easy containerization and horizontal scaling.

## Testing Requirements
- **Decision**: Unit + Integration testing approach
- **Rationale**: Critical to test both individual functions and AWS SQS integration behavior. Unit tests for business logic, integration tests for SQS operations.

## Additional Technical Assumptions and Requests
- **Language**: Go 1.21+ for performance, concurrency, and excellent AWS SDK support
- **HTTP Framework**: Standard library net/http or Gin framework for lightweight HTTP handling
- **AWS SDK**: AWS SDK for Go v2 for official SQS FIFO support
- **Configuration**: Environment variables for all configuration (AWS credentials, queue URL, port)
- **Logging**: Structured logging using standard library or lightweight logging package
- **Container Base Image**: Alpine Linux for minimal container size and security
- **Deployment**: Docker container ready for deployment on any container platform (ECS, EKS, Docker Compose)
- **Error Handling**: Graceful degradation with appropriate HTTP status codes and error messages
- **Health Checks**: Basic health endpoint for container orchestration platforms
